﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasevaluehelper.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年12月5日
* 摘要: 该文件定义了周期数据的辅助类, 目前主要用来在图谱绘制时, 调用此类的接口将数据转换成字符串

* 当前版本: 1.0
*/


#ifndef PHASEVALUEHELPER_H
#define PHASEVALUEHELPER_H

#include "phasedef.h"
#include <QDebug>
/* 说明:
 * 目前主要用来定义将数据转换为字符串的规则
 */
class PHASECHARTSHARED_EXPORT PhaseValueHelper
{
public:
    PhaseValueHelper() {}
    virtual ~PhaseValueHelper() {}

    /************************************************
     * 函数名: valueToString
     * 输入参数:
     *          value: 周期数据
     * 输出参数: NULL
     * 返回值: 转换结果
     * 功能: 将周期数据转换成字符串并返回
     ************************************************/
    virtual QString valueToString(Phase::ValueType value) const = 0;

    virtual Phase::SuffixValue suffixType() = 0;
};

class PHASECHARTSHARED_EXPORT VoltageValueHelper : public PhaseValueHelper
{
public:
    /************************************************
     * 函数名: valueToString
     * 输入参数:
     *          value: 周期数据
     * 输出参数: NULL
     * 返回值: 转换结果
     * 功能: 将周期的幅值数据转换成字符串并返回
     ************************************************/
    QString valueToString(Phase::ValueType value) const
    {
        QString strUnit = "mV";
        if(qAbs(value) >= 1000)
        {
            value /= 1000;
            strUnit = "V";
        }
        return QString::number(value, 'f', 1) + strUnit;
    }

    Phase::SuffixValue suffixType(  )
    {
        return Phase::SUFFIX_MV;
    }
};

class PHASECHARTSHARED_EXPORT DBValueHelper : public PhaseValueHelper
{
public:
    /************************************************
     * 函数名: valueToString
     * 输入参数:
     *          value: 周期数据
     * 输出参数: NULL
     * 返回值: 转换结果
     * 功能: 将周期的幅值数据转换成字符串并返回
     ************************************************/
    QString valueToString(Phase::ValueType value) const
    {
        QString strUnit = "dB";
        int iValue = (int)value;
        return QString::number(iValue) + strUnit;
    }

    Phase::SuffixValue suffixType(  )
    {
        return Phase::SUFFIX_DB;
    }
};

#endif // PHASEVALUEHELPER_H
