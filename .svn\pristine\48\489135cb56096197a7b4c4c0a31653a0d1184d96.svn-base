/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* LabelSliderPopup.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月10日
* 修改日期：2016年11月15日
*       重构
* 摘要：滑块，点击时发出信号sigCommit()

* 当前版本：1.0
*/
#ifndef SLIDER_H
#define SLIDER_H
#include <QSlider>

class Slider : public QSlider
{
    Q_OBJECT
public:
    /*****************************************************************
     * 功能     ：构造函数，初始化滑块类
     * 输入参数 ：
     *      orientation -- 方向
     *      parent -- 父窗体
     ****************************************************************/
    Slider( Qt::Orientation orientation, QWidget * parent = 0 );
protected:
    /*************************************************
    功能： 鼠标点击事件
    输入参数:
        event:事件
    *************************************************************/
    void mousePressEvent(QMouseEvent *event);
signals:
    /*************************************************
    功能： 信号，点击后提交
    *************************************************************/
    void sigCommit();
};

#endif // SLIDER_H
