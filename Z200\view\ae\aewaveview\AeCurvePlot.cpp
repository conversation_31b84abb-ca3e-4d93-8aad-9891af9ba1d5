#include <thirdparty/qwt/qwt_scale_draw.h>
#include <thirdparty/qwt/qwt_plot_layout.h>
#include <thirdparty/qwt/qwt_plot_canvas.h>
#include <thirdparty/qwt/qwt_scale_widget.h>
#include <thirdparty/qwt/qwt_scale_div.h>
#include <thirdparty/qwt/qwt_interval.h>
#include "AeCurvePlot.h"

class AECurvePlot::ScaleDraw : public QwtScaleDraw
{
public:
    /************************************************
     * 函数名    :ScaleDraw
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：X轴构造函数
     ************************************************/
    ScaleDraw()
    {
        setPenWidth(2);
        setTickLength( QwtScaleDiv::MajorTick, MARJOR_TICK_LENGTH );
        setTickLength( QwtScaleDiv::MinorTick, 0 );
        setTickLength( QwtScaleDiv::MediumTick, 0 );

        enableComponent(QwtScaleDraw::Labels, false);
    }


};

class AECurvePlot::YAxisScaleDraw : public QwtScaleDraw
{
public:
    /************************************************
     * 函数名    :YAxisScaleDraw
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：Y轴构造函数
     ************************************************/
    YAxisScaleDraw()
    {
        setPenWidth(2);
        setTickLength( QwtScaleDiv::MajorTick, MARJOR_TICK_LENGTH );
        setTickLength( QwtScaleDiv::MinorTick, 0 );
        setTickLength( QwtScaleDiv::MediumTick, 0 );
    }

    /************************************************
     * 函数名    :label
     * 输入参数  ：value - 刻度值
     * 输出参数  ：NULL
     * 返回值   ：QwtText - 标签文本
     * 功能     ：自定义Y轴标签显示，只显示上下限
     ************************************************/
    virtual QwtText label(double value) const
    {
        // 获取当前刻度范围
        QwtInterval interval = scaleDiv().interval();
        double minValue = interval.minValue();
        double maxValue = interval.maxValue();

        // 只在最大值和最小值位置显示标签
        if (qAbs(value - maxValue) < 0.01 || qAbs(value - minValue) < 0.01)
        {
            QString labelText = QString::number(static_cast<int>(value));
            // 使用固定宽度格式，右对齐，确保一位数和两位数占用相同空间
            labelText = QString("  %1").arg(labelText);
            QwtText text(labelText);
            QFont font = text.font();
            font.setPointSize(20);  // 设置字体大小与"幅值[mV]"一致
            text.setFont(font);
            return text;
        }

        // 其他位置不显示标签
        return QwtText("");
    }
};


/************************************************
 * 函数名    :AECurvePlot
 * 输入参数  ：X轴的最大刻度名称，父对象指针
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：构造函数
 ************************************************/
AECurvePlot::AECurvePlot(QWidget *parent)
    :QwtPlot(parent)
{
    setAxisAutoScale(QwtPlot::xBottom, false);
    setAxisScale(QwtPlot::xBottom, -1, NUM_POINTS, NUM_POINTS / NUM_X_AXIS_TICK);
    m_pScaleDraw = new ScaleDraw();
    setAxisScaleDraw(QwtPlot::xBottom, m_pScaleDraw);

    // 使用自定义Y轴ScaleDraw，只显示上下限标签
    m_pYAxisScaleDraw = new YAxisScaleDraw();
    setAxisScaleDraw(QwtPlot::yLeft, m_pYAxisScaleDraw);

    // 禁用Y轴标题，改为在drawCanvas中自定义绘制
    setAxisTitle(QwtPlot::yLeft, QwtText(""));

    QwtPlotCanvas *canvas = new QwtPlotCanvas();
    canvas->setFrameStyle( QFrame::Box | QFrame::Plain );
    canvas->setLineWidth( 2 );
    canvas->setPalette( Qt::white );
    setCanvas( canvas );

    setAxisLabelAlignment(QwtPlot::xBottom, Qt::AlignTop);

    m_pCurve = new QwtPlotCurve();
    m_pCurve->setPen(Qt::blue);
    m_pCurve->setCurveAttribute(QwtPlotCurve::Fitted, false);
    m_pCurve->attach(this);

    m_pMarker = new QwtPlotMarker();
    m_pMarker->setLineStyle(QwtPlotMarker::HLine);
    m_pMarker->setLinePen(Qt::red, MARKER_LINE_WIDTH, Qt::DotLine);

    // 不使用QWT的网格系统，改用自定义绘制6×6棋盘网格

    alignScales();
}

/************************************************
 * 函数名    :setSamples
 * 输入参数  ：采样数据
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置曲线数据
 ************************************************/
void AECurvePlot::setSamples(const QVector<AE::WaveData>& data)
{
    QVector<double> xData(data.size());
    QVector<double> yData(data.size());
    for(int i = 0; i < data.size(); ++i)
    {
        xData[i] = i;
        yData[i] = data.at(i).fWaveValue;
    }

    setAxisScale(QwtPlot::xBottom, -1, data.size(), data.size() / NUM_X_AXIS_TICK);

    if(!m_pCurve->isVisible())
    {
        m_pCurve->show();
    }

    m_pCurve->setSamples(xData, yData);

    replot();
}

/************************************************
 * 函数名    :setMarkerValue
 * 输入参数  ：标记线的y坐标值
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置触发值
 ************************************************/
void AECurvePlot::setMarkerValue(double value)
{
    m_pMarker->setYValue(value);
    replot();
}

/************************************************
 * 函数名    :setMarkerValue
 * 输入参数  ：标记线的y坐标值
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置触发值
 ************************************************/
void AECurvePlot::enableMarker(bool bEnabled)
{
    if(bEnabled)
    {
        m_pMarker->attach(this);
    }
    else
    {
        m_pMarker->detach();
    }
    replot();
}

/************************************************
 * 函数名    :setYAxisTitle
 * 输入参数  ：strTitle - Y轴标题
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置Y轴标题
 ************************************************/
void AECurvePlot::setYAxisTitle(const QString& strTitle)
{
    m_strYAxisTitle = strTitle;
    update(); // 触发重绘
}

/************************************************
 * 函数名    :clearCurve
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：清除波形数据
 ************************************************/
void AECurvePlot::clearCurve()
{
    QVector<double> xData(NUM_POINTS, 0);
    QVector<double> yData(NUM_POINTS, -1);

    m_pCurve->setSamples(xData, yData);
    m_pCurve->setVisible(false);

    replot();
}

/************************************************
 * 函数名    :alignScales
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：调整坐标轴和画布的布局
 ************************************************/
void AECurvePlot::alignScales()
{
    for ( int i = 0; i < QwtPlot::axisCnt; i++ )
    {
        QwtScaleWidget *scaleWidget = axisWidget( i );
        if ( scaleWidget )
        {
            scaleWidget->setMargin( 0 );
        }

        QwtScaleDraw *scaleDraw = axisScaleDraw( i );
        if ( scaleDraw )
            scaleDraw->enableComponent( QwtAbstractScaleDraw::Backbone, false );
    }

    plotLayout()->setAlignCanvasToScales( true );
}

/************************************************
 * 函数名    :drawCanvas
 * 输入参数  ：painter - 绘制器
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：重写绘制函数，添加6×6网格
 ************************************************/
void AECurvePlot::drawCanvas(QPainter *painter)
{
    // 先调用基类的绘制方法
    QwtPlot::drawCanvas(painter);

    // 获取画布区域
    QRect canvasRect = canvas()->contentsRect();

    // 绘制12×6棋盘网格 {{ AURA-X: Modify - X轴网格与12等分刻度对应 }}
    painter->save();
    painter->setPen(QPen(Qt::gray, 1, Qt::SolidLine));

    // 绘制垂直网格线（13条线形成12列）{{ AURA-X: Modify - 与12等分刻度对应 }}
    for(int i = 0; i <= WAVE_GRID_COUNT_X; ++i)
    {
        int x = canvasRect.left() + canvasRect.width() * i / WAVE_GRID_COUNT_X;
        painter->drawLine(x, canvasRect.top(), x, canvasRect.bottom());
    }

    // 绘制水平网格线（7条线形成6行）
    for(int i = 0; i <= WAVE_GRID_COUNT_Y; ++i)
    {
        int y = canvasRect.top() + canvasRect.height() * i / WAVE_GRID_COUNT_Y;
        painter->drawLine(canvasRect.left(), y, canvasRect.right(), y);
    }


    painter->restore();
}

/************************************************
 * 函数名    :paintEvent
 * 输入参数  ：event - 绘制事件
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：重写绘制事件，在画布外部绘制Y轴标题
 ************************************************/
void AECurvePlot::paintEvent(QPaintEvent *event)
{
    // 先调用基类的绘制方法
    QwtPlot::paintEvent(event);

    // 在画布外部绘制Y轴标题，与数值标签在同一列
    QPainter painter(this);
    painter.setPen(QPen(Qt::black, 1, Qt::SolidLine));
    QFont font = painter.font();
    font.setPointSize(20);
    painter.setFont(font);

    // 获取画布区域和Y轴区域
    QRect canvasRect = canvas()->contentsRect();
    QwtScaleWidget *yAxisWidget = axisWidget(QwtPlot::yLeft);

    if (yAxisWidget && !m_strYAxisTitle.isEmpty()) {
        // 旋转画布-90度，让文字垂直显示
        painter.rotate(-90);

        // 动态计算Y轴标题的位置
        QRect yAxisRect = yAxisWidget->geometry();
        QFontMetrics fm(font);

        // Y轴中心位置（垂直居中）
        int yAxisCenter = -(canvasRect.top() + canvasRect.height() / 2);

        // 标题文本宽度的一半，用于居中显示
        int titleHalfWidth = fm.width(m_strYAxisTitle) / 2;

        // X坐标位置：Y轴区域的右侧边缘减去一些边距
        int labelXPos = yAxisRect.width() - fm.height() / 2;

        // 绘制标题，垂直和水平都居中
        painter.drawText(yAxisCenter - titleHalfWidth, labelXPos, m_strYAxisTitle);

        painter.rotate(90);
    }
}
