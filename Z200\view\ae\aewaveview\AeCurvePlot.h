#ifndef AECURVEPLOT_H
#define AECURVEPLOT_H

#include <thirdparty/qwt/qwt_plot_curve.h>
#include <thirdparty/qwt/qwt_plot.h>
#include <thirdparty/qwt/qwt_plot_marker.h>
#include "datadefine.h"
#include "ae/AE.h"

const INT32 MARJOR_TICK_LENGTH = 6;      // X轴刻度线的长度
const INT32 NUM_POINTS = 250;            // 曲线点数
const INT32 NUM_X_AXIS_TICK = 12;        // X轴的刻度数
//const INT32 TICK_TEXT_SPACING = 15;      // 设置坐标轴与文字之间的距离
const double MARKER_LINE_WIDTH = 1.5;    // 标记线的宽度

// 棋盘网格配置宏定义
#define WAVE_GRID_COUNT_X    12   // X轴网格数量 {{ AURA-X: Modify - 将X轴网格从6改为12，与刻度对应 }}
#define WAVE_GRID_COUNT_Y    6    // Y轴网格数量



class AECurvePlot : public QwtPlot
{
public:
    /************************************************
     * 函数名    :AECurvePlot
     * 输入参数  ：X轴的最大刻度名称，父对象指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：构造函数
     ************************************************/
    AECurvePlot(QWidget *parent = NULL);

    /************************************************
     * 输入参数  ：采样数据
     * 功能     ：设置曲线数据
     ************************************************/
    void setSamples(const QVector<AE::WaveData>& data);

    /************************************************
     * 函数名    :setMarkerValue
     * 输入参数  ：标记线的y坐标值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置触发值
     ************************************************/
    void setMarkerValue(double value);

    /************************************************
     * 函数名    :setMarkerValue
     * 输入参数  ：标记线的y坐标值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置触发值
     ************************************************/
    void enableMarker(bool bEnabled);

    /************************************************
     * 函数名    :setYAxisTitle
     * 输入参数  ：strTitle - Y轴标题
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置Y轴标题
     ************************************************/
    void setYAxisTitle(const QString& strTitle);

    /************************************************
     * 函数名    :clearCurve
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：清除波形数据
     ************************************************/
    void clearCurve();

protected:
    /************************************************
     * 函数名    :drawCanvas
     * 输入参数  ：painter - 绘制器
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：重写绘制函数，添加6×6网格
     ************************************************/
    virtual void drawCanvas(QPainter *painter);

    /************************************************
     * 函数名    :paintEvent
     * 输入参数  ：event - 绘制事件
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：重写绘制事件，在画布外部绘制Y轴标题
     ************************************************/
    virtual void paintEvent(QPaintEvent *event);

private:
    /************************************************
     * 函数名    :alignScales
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：调整坐标轴和画布的布局
     ************************************************/
    void alignScales();

private:
    QwtPlotCurve *m_pCurve;             // 曲线
    QwtPlotMarker *m_pMarker;           // 触发线

    class ScaleDraw;                    // X轴坐标轴绘制类
    class YAxisScaleDraw;               // Y轴坐标轴绘制类
    ScaleDraw *m_pScaleDraw;            // 实现X轴的绘制
    YAxisScaleDraw *m_pYAxisScaleDraw;  // 实现Y轴的绘制

    QString m_strYAxisTitle;            // Y轴标题
};

#endif // AECURVEPLOT_H
