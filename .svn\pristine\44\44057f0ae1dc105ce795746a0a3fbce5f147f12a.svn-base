/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：mechanicalspectrumprivate.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/08
 * 摘要：mechanical图谱数据私有定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <vector>

namespace DataSpecificationNS
{
    struct MechanicalExtInformationPrivate
    {
        friend class MechanicalSpectrum;

        quint8 ucAmpUnit;
        float fAmpLowerLimit;
        float fAmpUpperLimit;
        qint32 iDataPoint;
        qint64 llSampleRate;
        quint8 ucSwitchingOperation;
        qint32 iPowerFreqCycleCount;

        void skipAmpUnit() { bSkipAmpUnit = true; }
        void skipAmpLowerLimit() { bSkipAmpLowerLimit = true; }
        void skipAmpUpperLimit() { bSkipAmpUpperLimit = true; }
        void skipDataPoint() { bSkipDataPoint = true; }
        void skipSampleRate() { bSkipSampleRate = true; }
        void skipSwitchingOperation() { bSkipSwitchingOperation = true; }
        void skipPowerFreqCycleCount() { bSkipPowerFreqCycleCount = true; }

        bool hasAmpUnit() const { return bHasAmpUnit; }
        bool hasAmpLowerLimit() const { return bHasAmpLowerLimit; }
        bool hasAmpUpperLimit() const { return bHasAmpUpperLimit; }
        bool hasDataPoint() const { return bHasDataPoint; }
        bool hasSampleRate() const { return bHasSampleRate; }
        bool hasSwitchingOperation() const { return bHasSwitchingOperation; }
        bool hasPowerFreqCycleCount() const { return bHasPowerFreqCycleCount; }

        MechanicalExtInformationPrivate();
        MechanicalExtInformationPrivate& operator=(const MechanicalExtInformationPrivate& stMechanicalExtInformationPrivate);
        bool operator==(const MechanicalExtInformationPrivate& stMechanicalExtInformationPrivate) const;

    private:
        bool bSkipAmpUnit;
        bool bSkipAmpLowerLimit;
        bool bSkipAmpUpperLimit;
        bool bSkipDataPoint;
        bool bSkipSampleRate;
        bool bSkipSwitchingOperation;
        bool bSkipPowerFreqCycleCount;

        bool bHasAmpUnit;
        bool bHasAmpLowerLimit;
        bool bHasAmpUpperLimit;
        bool bHasDataPoint;
        bool bHasSampleRate;
        bool bHasSwitchingOperation;
        bool bHasPowerFreqCycleCount;
    };

    struct MechanicalCoilExtInformationPrivate
    {
        friend class MechanicalCoilSpectrum;

        quint8 ucMechDataType;
        quint8 ucOperatingMechanismType;
        quint8 ucMechStoredEnergyType;
        float fCoilUpTime;
        float fHitTime;
        float fAuxSubSwitchCloseTime;
        float fCoilDownTime;
        float fCoilMaxCurrent;
        float fConversionFactor;

        void skipMechDataType() { bSkipMechDataType = true; }
        void skipOperatingMechanismType() { bSkipOperatingMechanismType = true; }
        void skipMechStoredEnergyType() { bSkipMechStoredEnergyType = true; }
        void skipCoilUpTime() { bSkipCoilUpTime = true; }
        void skipHitTime() { bSkipHitTime = true; }
        void skipAuxSubSwitchCloseTime() { bSkipAuxSubSwitchCloseTime = true; }
        void skipCoilDownTime() { bSkipCoilDownTime = true; }
        void skipCoilMaxCurrent() { bSkipCoilMaxCurrent = true; }
        void skipConversionFactor() { bSkipConversionFactor = true; }

        bool hasMechDataType() const { return bHasMechDataType; }
        bool hasOperatingMechanismType() const { return bHasOperatingMechanismType; }
        bool hasMechStoredEnergyType() const { return bHasMechStoredEnergyType; }
        bool hasCoilUpTime() const { return bHasCoilUpTime; }
        bool hasHitTime() const { return bHaHitTime; }
        bool hasAuxSubSwitchCloseTime() const { return bHasAuxSubSwitchCloseTime; }
        bool hasCoilDownTime() const { return bHasCoilDownTime; }
        bool hasCoilMaxCurrent() const { return bHasCoilMaxCurrent; }
        bool hasConversionFactor() const { return bHasConversionFactor; }

        MechanicalCoilExtInformationPrivate();
        MechanicalCoilExtInformationPrivate& operator=(const MechanicalCoilExtInformationPrivate& stMechanicalCoilExtInformationPrivate);
        bool operator==(const MechanicalCoilExtInformationPrivate& stMechanicalCoilExtInformationPrivate) const;

    private:
        bool bSkipMechDataType;
        bool bSkipOperatingMechanismType;
        bool bSkipMechStoredEnergyType;
        bool bSkipCoilUpTime;
        bool bSkipHitTime;
        bool bSkipAuxSubSwitchCloseTime;
        bool bSkipCoilDownTime;
        bool bSkipCoilMaxCurrent;
        bool bSkipConversionFactor;

        bool bHasMechDataType;
        bool bHasOperatingMechanismType;
        bool bHasMechStoredEnergyType;
        bool bHasCoilUpTime;
        bool bHaHitTime;
        bool bHasAuxSubSwitchCloseTime;
        bool bHasCoilDownTime;
        bool bHasCoilMaxCurrent;
        bool bHasConversionFactor;
    };

    struct MechanicalMotorExtInformationPrivate
    {
        friend class MechanicalMotorSpectrum;

        quint8 ucMechDataType;
        quint8 ucOperatingMechanismType;
        quint8 ucMechStoredEnergyType;
        float fMotorStartCurrent;
        float fMotorMaxCurrent;
        float fMotorStorageTime;
        float fConversionFactor;

        void skipMechDataType() { bSkipMechDataType = true; }
        void skipOperatingMechanismType() { bSkipOperatingMechanismType = true; }
        void skipMechStoredEnergyType() { bSkipMechStoredEnergyType = true; }
        void skipMotorStartCurrent() { bSkipMotorStartCurrent = true; }
        void skipMotorMaxCurrent() { bSkipMotorMaxCurrent = true; }
        void skipMotorStorageTime() { bSkipMotorStorageTime = true; }
        void skipConversionFactor() { bSkipConversionFactor = true; }

        bool hasmechDataType() const { return bHasmechDataType; }
        bool hasOperatingMechanismType() const { return bHasOperatingMechanismType; }
        bool hasMechStoredEnergyType() const { return bHasMechStoredEnergyType; }
        bool hasMotorStartCurrent() const { return bHasMotorStartCurrent; }
        bool hasMotorMaxCurrent() const { return bHasMotorMaxCurrent; }
        bool hasMotorStorageTime() const { return bHasMotorStorageTime; }
        bool hasConversionFactor() const { return bHasConversionFactor; }

        MechanicalMotorExtInformationPrivate();
        MechanicalMotorExtInformationPrivate& operator=(const MechanicalMotorExtInformationPrivate& stMechanicalMotorExtInformationPrivate);
        bool operator==(const MechanicalMotorExtInformationPrivate& stMechanicalMotorExtInformationPrivate) const;

    private:
        bool bSkipMechDataType;
        bool bSkipOperatingMechanismType;
        bool bSkipMechStoredEnergyType;
        bool bSkipMotorStartCurrent;
        bool bSkipMotorMaxCurrent;
        bool bSkipMotorStorageTime;
        bool bSkipConversionFactor;

        bool bHasmechDataType;
        bool bHasOperatingMechanismType;
        bool bHasMechStoredEnergyType;
        bool bHasMotorStartCurrent;
        bool bHasMotorMaxCurrent;
        bool bHasMotorStorageTime;
        bool bHasConversionFactor;
    };

    struct MechanicalSwitchExtInformationPrivate
    {
        friend class MechanicalSwitchSpectrum;

        quint8 ucMechDataType;
        quint8 ucOperatingMechanismType;
        quint8 ucMechStoredEnergyType;
        float fSwitchOpenTime;
        float fSwitchCloseTime;
        float fSwitchTwiceTime;
        float fSwitchOpenSync;
        float fSwitchCloseSync;
        float fSwitchTwiceSync;
        float fSwitchShotTime;
        float fSwitchNoCurrentTime;
        float fConversionFactor;

        void skipMechDataType() { bSkipMechDataType = true; }
        void skipOperatingMechanismType() { bSkipOperatingMechanismType = true; }
        void skipMechStoredEnergyType() { bSkipMechStoredEnergyType = true; }
        void skip_switchOpenTime() { bSkipswitchOpenTime = true; }
        void skip_switchCloseTime() { bSkipswitchCloseTime = true; }
        void skip_switchTwiceTime() { bSkipswitchTwiceTime = true; }
        void skip_switchOpenSync() { bSkipswitchOpenSync = true; }
        void skip_switchCloseSync() { bSkipswitchCloseSync = true; }
        void skip_switchTwiceSync() { bSkipswitchTwiceSync = true; }
        void skip_switchShotTime() { bSkipswitchShotTime = true; }
        void skip_switchNoCurrentTime() { bSkipswitchNoCurrentTime = true; }
        void skipConversionFactor() { bSkipConversionFactor = true; }

        bool hasmechDataType() const { return bHasmechDataType; }
        bool hasOperatingMechanismType() const { return bHasOperatingMechanismType; }
        bool hasMechStoredEnergyType() const { return bHasMechStoredEnergyType; }
        bool hasswitchOpenTime() const { return bHasswitchOpenTime; }
        bool hasswitchCloseTime() const { return bHasswitchCloseTime; }
        bool hasswitchTwiceTime() const { return bHasswitchTwiceTime; }
        bool hasswitchOpenSync() const { return bHasswitchOpenSync; }
        bool hasswitchCloseSync() const { return bHasswitchCloseSync; }
        bool hasswitchTwiceSync() const { return bHasswitchTwiceSync; }
        bool hasswitchShotTime() const { return bHasswitchShotTime; }
        bool hasswitchNoCurrentTime() const { return bHasswitchNoCurrentTime; }
        bool hasConversionFactor() const { return bHasConversionFactor; }

        MechanicalSwitchExtInformationPrivate();
        MechanicalSwitchExtInformationPrivate& operator=(const MechanicalSwitchExtInformationPrivate& stMechanicalSwitchExtInformationPrivate);
        bool operator==(const MechanicalSwitchExtInformationPrivate& stMechanicalSwitchExtInformationPrivate) const;

    private:
        bool bSkipMechDataType;
        bool bSkipOperatingMechanismType;
        bool bSkipMechStoredEnergyType;
        bool bSkipswitchOpenTime;
        bool bSkipswitchCloseTime;
        bool bSkipswitchTwiceTime;
        bool bSkipswitchOpenSync;
        bool bSkipswitchCloseSync;
        bool bSkipswitchTwiceSync;
        bool bSkipswitchShotTime;
        bool bSkipswitchNoCurrentTime;
        bool bSkipConversionFactor;

        bool bHasmechDataType;
        bool bHasOperatingMechanismType;
        bool bHasMechStoredEnergyType;
        bool bHasswitchOpenTime;
        bool bHasswitchCloseTime;
        bool bHasswitchTwiceTime;
        bool bHasswitchOpenSync;
        bool bHasswitchCloseSync;
        bool bHasswitchTwiceSync;
        bool bHasswitchShotTime;
        bool bHasswitchNoCurrentTime;
        bool bHasConversionFactor;
    };

    struct MechanicalDataPrivate
    {
        friend class MechanicalSpectrum;

        QByteArray qbaMechStorageData;

        void skipMechStorageData() { bSkipMechStorageData = true; }

        bool hasMechStorageData() const { return bHasMechStorageData; }

        MechanicalDataPrivate();
        MechanicalDataPrivate& operator=(const MechanicalDataPrivate& stMechanicalDataPrivate);
        bool operator==(const MechanicalDataPrivate& stMechanicalDataPrivate) const;

    private:
        bool bSkipMechStorageData;

        bool bHasMechStorageData;
    };
}
