/*
 * Copyright (c) 2019.6，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentdetectionview.h
 *
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/06/27
 * 摘要：电流检测视图类
 * 当前版本：1.0
 */

#pragma once

#include "widgets/sampleChartView/SampleChartView.h"
#include "currentdetection/currentdetectiondefine.h"
#include "currentdetection/currentdetectionservice.h"

class BJCurrentDetectionChart;
class BJCurrentDetectionView : public SampleChartView
{
    Q_OBJECT

public:
    explicit BJCurrentDetectionView(QWidget *parent = NULL);
    virtual ~BJCurrentDetectionView();

protected:
    /*************************************************
    函数名： onSKeyPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

private:
    /*************************************************
    函数名： initDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /*************************************************
    函数名： setChartDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱数据
    *************************************************************/
    void setChartDatas();

    /*************************************************
    函数名： saveData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存数据
    *************************************************************/
    void saveData();

    /*************************************************
    函数名： loadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 载入数据
    *************************************************************/
    void loadData();

    /*************************************************
    函数名： deleteData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 删除数据
    *************************************************************/
    void deleteData();

    /*************************************************
    函数名： saveDataToFile(const QString &stationName, const QString& deviceName)
    输入参数：
        stationName：站名
        deviceName：设备名
    输出参数： NULL
    返回值： NULL
    功能：保存数据文件
    *************************************************************/
    virtual QString saveDataToFile();

    /*************************************************
    函数名： startSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 开始采样
    *************************************************************/
    void startSample();

    /*************************************************
    函数名： stopSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 停止采样
    *************************************************************/
    void stopSample();

    /*************************************************
    函数名： switchSampleState()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 切换采样状态
    *************************************************************/
    void switchSampleState();

    /*************************************************
    函数名： saveConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存配置信息
    *************************************************************/
    void saveConfig();

private slots:
    /*************************************************
    函数名： onButtonValueChanged(int id, int iValue)
    输入参数： id：按钮ID
              iValue：按钮值
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮值变化事件
    *************************************************************/
    void onButtonValueChanged(int id, int iValue);

    /*************************************************
    函数名： onCommandButtonPressed(int id)
    输入参数： id：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 响应命令按钮按下事件
    *************************************************************/
    void onCommandButtonPressed(int id);

    /*************************************************
    函数名： onDataRead(CurrentDetection::CurrentDetectionData data, MultiServiceNS::USERID userId)
    输入参数： data：电流数据
    输出参数： NULL
    返回值： NULL
    功能： 响应读取的数据
    *************************************************************/
    void onDataRead(CurrentDetection::CurrentDetectionData data, MultiServiceNS::USERID userId);

    /*************************************************
    函数名： onSignalChanged(Module::SignalState eSignalState)
    输入参数： eSignalState：信号状态
    输出参数： NULL
    返回值： NULL
    功能： 响应信号状态改变
    *************************************************************/
    void onSignalChanged(Module::SignalState eSignalState);

    /*************************************************
    函数名： onPhaseTypeChanged(const CurrentDetection::CurrentPhaseType eCurrentPhaseType)
    输入参数： eCurrentPhaseType：相别类型
    输出参数： NULL
    返回值： NULL
    功能： 响应信号相别类型改变
    *************************************************************/
    void onPhaseTypeChanged(const CurrentDetection::CurrentPhaseType eCurrentPhaseType);

private:
    BJCurrentDetectionChart* m_pChart;
    CurrentDetectionService* m_pService;

    MultiServiceNS::USERID m_iUserId; // 服务模块分配的用户id，用以识别用户

    bool m_bIsSampling;         // 是否正在采样

    //UINT16 m_uGroundingAlarmValue;
    //UINT16 m_uLoadAlarmValue;
};
