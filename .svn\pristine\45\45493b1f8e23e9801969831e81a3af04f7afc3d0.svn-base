/*
* Copyright (c) 2017.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: intvalidator.h
*
* 初始版本：1.0
* 作者： zhanglang
* 创建日期：2018年10月25日
* 摘要： 该文件主要定义了int验证器
*       通过父类的setRange方法设置minVal,maxVal,decimals；
*       原QIntValidator类不能校验开头00输入，不够完善

* 当前版本：1.0
*/

#ifndef INTVALIDATOR_H
#define INTVALIDATOR_H

#include <QIntValidator>

class IntValidator : public QIntValidator
{
public:
    /*************************************************
    功能： 构造函数
    输入参数： parent--父指针
    输出参数： NULL
    返回值： NULL
    *************************************************/
    explicit IntValidator(QObject *parent);

    /*************************************************
    功能： 校验输入（内容改变自动触发该函数）
    输入参数： input--输入内容；pos--光标位置
    输出参数： NULL
    返回值： QValidator::State--校验结果
    *************************************************/
    virtual QValidator::State validate(QString &input, int &pos) const;

};

#endif // INTVALIDATOR_H
