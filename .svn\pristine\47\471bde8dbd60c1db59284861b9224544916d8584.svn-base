/*****< btpmset.h >************************************************************/
/*      Copyright 2010 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  BTPMSET - Stonestreet One Bluetooth Protocol Stack Platform Manager       */
/*            settings and configuration functions  that can be utilized by   */
/*            the Platform Manager to read/write configuration data on the    */
/*            device.                                                         */
/*                                                                            */
/*  Author:  <PERSON>                                                      */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   05/23/10  D. Lange       Initial creation.                               */
/******************************************************************************/
#ifndef __BTPMSETH__
#define __BTPMSETH__

#include "BTAPITyp.h"            /* Bluetooth API Type Definitions.           */

#include "SETAPI.h"              /* BTPM Settings/Config Protypes/Constants.  */

   /* The following function is a utility function that exists to       */
   /* initialize the Configuration Utility Module.  This function       */
   /* accepts as input platform specific configuration information.     */
void SET_Initialize(SET_Initialization_Data_t *SettingsInitializationInfo);

   /* The following function is a utility function that is provided     */
   /* allow a mechanism to inform the Configuration Module that it      */
   /* should free any resources that it currently occupies.  After this */
   /* function is called, no other functions in this module can be      */
   /* utilized until the SET_Initialize() function is called.           */
void SET_Cleanup(void);

#endif
