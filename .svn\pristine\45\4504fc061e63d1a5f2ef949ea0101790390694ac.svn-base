#ifndef AMPMAPDEFINE_H
#define AMPMAPDEFINE_H

#include <QObject>
#include "datafiledefine.h"

namespace AmpMapNS
{
    // 幅值数据
    typedef struct _AmpData
    {
        _AmpData()
        {
            iCurrentAmp = 0;
            iCurrentMax = 0;
        }
        qint8 iCurrentAmp;  //当前值，单位以幅值单位为准
        qint8 iCurrentMax;  //最大值，单位以幅值单位为准
    }AmpData;

    typedef struct _AmpBinaryData
    {
        _AmpBinaryData()
        {
            fHFCTAmp = 0;
            fHFCTMax = 0;
            iPulseCount = 1;
            strBGFileName.reserve(53);
        }
        float fHFCTAmp;//暂态地电压幅值数据
        float fHFCTMax;//暂态地电压幅值最大值
        qint32 iPulseCount;//脉冲数
        QString strBGFileName;
    }AmpBinaryData;

    typedef struct _AmpMapInfo
    {
        _AmpMapInfo()
        {
            eAmpUnit = DataFileNS::AMP_UNIT_DB;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eBandWidth = DataFileNS::BAND_DEFAULT;
            fFrequencyMin = 0;
            fFrequencyMax = 0;
            fWarningValue = 20;
            fAlarmingValue = 40;
            memset(ucaDischargeTypeProb, 0, sizeof(ucaDischargeTypeProb));
            eDataSign = DataFileNS::DATA_DEFAULT;
            eGainType = DataFileNS::GAIN_TYPE_DEFAULT;
            sGain = 0;
            fSyncFreq = -1;
        }

        DataFileNS::AmpUnit eAmpUnit; //幅值单位
        float fAmpLowerLimit;//幅值下限
        float fAmpUpperLimit;//幅值上限
        DataFileNS::MapBandWidth eBandWidth;//超声传感器类型
        float fFrequencyMin;//下限频率
        float fFrequencyMax;//上限频率
        float fWarningValue;                //预警值
        float fAlarmingValue;               //报警值
        quint8 ucaDischargeTypeProb[8];//放电类型概率
        DataFileNS::MapDataSign eDataSign;//数据有效判断标志
        DataFileNS::GainType eGainType; //增益种类
        qint16 sGain;//增益, 60dB；80dB；100dB
        float fSyncFreq;//标识测试系统频率，如果没有同步频率，则存-1
    }AmpMapInfo;

    typedef struct _AmpBinaryMapInfo
    {
        _AmpBinaryMapInfo()
        {
            eAmpUnit = DataFileNS::AMP_UNIT_DB;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 60;
        }
        DataFileNS::AmpUnit eAmpUnit; //幅值单位
        float fAmpLowerLimit;//幅值下限
        float fAmpUpperLimit;//幅值上限
    }AmpBinaryMapInfo;

}



#endif // AMPMAPDEFINE_H

