#include "hfcthistogramchart.h"
#include "hfct/HFCTViewConfig.h"

/************************************************
 * 函数名    :HFCTHistogramChart
 * 输入参数  ：条形的数目， 最小数值，最大数值， 父对象指针
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：构造函数
 ************************************************/
HFCTHistogramChart::HFCTHistogramChart(UINT16 numColumns, INT32 minValue, INT32 maxValue,INT32 chartHeight, QWidget *parent) :
    HistogramChart(numColumns,minValue,maxValue,chartHeight,parent)
{
}

/************************************************
 * 输入参数  ：eGain -- 增益值
 * 功能     ：设置增益
 ************************************************/
void HFCTHistogramChart::setGain( HFCT::Gain eGain )
{
    int iMin = 0;
    iMin = HFCT::GAIN_VALUES[eGain];
    setRange( iMin,HFCT::GAIN_BASE + iMin );
}
