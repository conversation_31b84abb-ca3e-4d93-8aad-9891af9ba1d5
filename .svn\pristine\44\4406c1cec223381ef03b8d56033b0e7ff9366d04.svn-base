/*
* Copyright (c) 2019.04，南京华乘电气科技有限公司
* All rights reserved.
*
* commonitemlistview.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2019年04月01日
* 摘要：定义item信息列表界面

* 当前版本：1.0
*/

#ifndef COMMONITEMLISTVIEW_H
#define COMMONITEMLISTVIEW_H

#include <QFrame>
#include <QListWidget>
#include "titlebar/TitleBar.h"

class CommonItemListView : public QFrame
{
    Q_OBJECT
public:
    typedef enum _ErrorInfo_
    {
        ERROR_INDEX = -1,
    }ErrorInfo;

    typedef enum _OperMode_
    {
        OPER_INVALID = -1,
        OPER_DELETE = 0,
        OPER_LOAD,
    }OperMode;

    /****************************
    功能： 构造函数
    参数： title--标题
          lItems -- 条目信息
          parent -- 父窗体
    *****************************/
    explicit CommonItemListView(const QString& title,
                            const QList<QString> &lItems,
                            QWidget *parent = 0);

    /****************************
    功能： 获得标题栏
    返回值:
        标题栏
    *****************************/
    TitleBar *titleBar( void );

    /*************************************************
    功能：设置选中条目的序号
    入参:
        iIndex -- 选中条目的序号
    *************************************************************/
    void setSelectedIndex( int iIndex );

    /*************************************************
    功能：被选中条目的序号
    返回值:
        被选中条目的序号
    *************************************************************/
    qint32 selectedIndex( void );

    /*************************************************
    功能：向前移动
    *************************************************************/
    void moveForward( void );

    /*************************************************
    功能：向后移动
    *************************************************************/
    void moveBackward( void );

    /*************************************************
    功能：enter键按下
    *************************************************************/
    void enterPressed( void );

    /*************************************************
    功能：添加条目信息
    入参:
        lItems -- 条目信息
    *************************************************************/
    void addItems( const QList<QString> &lItems );

    /*************************************************
    功能：删除所有item
    *************************************************************/
    void clear( void );

    /*************************************************
    功能：设置操作模式
    *************************************************************/
    void setOperMode(OperMode eMode);

signals:
    /*************************************************
    功能：信号 发射被选中条目的序号
    输入参数:
        iIndex:被选中条目的序号
    *************************************************************/
    void sigItemSelected( qint32 iIndex );

    /****************************
    信号:按下Enter键
    *****************************/
    void sigEnterPressed( void );

    /****************************
    信号:关闭
    *****************************/
    void sigClosed( void );

private slots:
    /*************************************************
    功能：槽  条目被点击或enter时触发
    输入参数:
        item:被选中条目
    *************************************************************/
    void onItemSelected(  QListWidgetItem * item );

    /*************************************************
    功能：槽  上翻页
    *************************************************************/
    void onUpPage( void );

    /*************************************************
    功能：槽  上翻页
    *************************************************************/
    void onDownPage( void );

    /*************************************************
    功能：槽  延迟触发列表选中功能
    *************************************************************/
    void onDelayItemSelected();

protected:
    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
    功能： 关闭事件
    输入参数:
        event -- 事件
    *************************************************************/
    void closeEvent(QCloseEvent *e);

    /*************************************************
    功能：resize事件处理
    *************************************************************/
    void resizeEvent(QResizeEvent *);

private:
    /*************************************************
    功能： 创建item列表
    输入参数:
        lTestInfos -- item信息
    *************************************************************/
    void createItemList( const QList<QString>& lTestInfos );
private:
    QListWidget *m_pListWidget;     // 提供可供滚动的框架窗体
    qint32 m_iIndexSelected;        // 选中的条目序号
    QList<QString> m_lTestDatas;    // 测试项的集合
    qint32 m_iMaxItemPerPage;       // 单页显示的最大条目数
    QWidget *m_pPageWidget;         // 存放翻页按键的占位窗体
    TitleBar *m_pTitleBar;           // 标题栏
    OperMode m_eOperMode;
};

#endif // COMMONITEMLISTVIEW_H
