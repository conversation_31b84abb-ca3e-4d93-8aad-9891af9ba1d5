/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* UHFAmplitudeView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月9日
* 修改日期：2017年2月7日
*       新版本重构
* 摘要：UHF幅值图谱定义

* 当前版本：1.0
*/

#ifndef UHFAMPLITUDEVIEW_H
#define UHFAMPLITUDEVIEW_H

#include "widgets/sampleChartView/SampleChartView.h"
#include "uhf/UHF.h"
#include "uhf/uhfampservice.h"
#include "widgets/histogram/HistogramChart.h"
#include "config/ConfigManager.h"
#include "Module.h"

class UHFAmplitudeView : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit UHFAmplitudeView( const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~UHFAmplitudeView( );

protected:
    /*************************************************
    功能： 响应S键事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onSKeyPressed();

    /****************************************************
     * 功能：保存操作
     * **************************************************/
    void pressSaveData();

    /*************************************************
    函数名：service
    输入参数: NULL
    输出参数：NULL
    返回值：service对象
    功能：返回service对象
    *************************************************************/
    UHFAmpService* getUHFAmpService();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

private slots:
    /*************************************************
    功能： 槽，响应读取的数据
    输入参数：
            data -- 幅值数据
    *************************************************************/
    void onDataRead(UHF::AmplitudeData data,MultiServiceNS::USERID userId);

    /*************************************************
    功能： 槽，响应信号状态改变
    输入参数：
            eSignalState -- 信号状态
    *************************************************************/
    void onSignalChanged( Module::SignalState eSignalState );

private:
    /*************************************************
    功能： 初始化数据
    *************************************************************/
    void initDatas( void );

    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarDatas( void );

    /*************************************************
    功能： 设置表格数据
    *************************************************************/
    void setChartDatas( void );

    /*************************************************
    功能： 恢复默认
    *************************************************************/
    void restoreDefault( void );

    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig( void );

    /*************************************************
    功能： 重置报警幅值（黄色报警最大值应当低于红色报警）
    *************************************************************/
    void resetAlarmScope( void );

    /*************************************************
    功能： 设置工作参数
    *************************************************************/
    void setWorksets( void );

    /*************************************************
    功能： 设置工作模式
    *************************************************************/
    void setWorkMode( UHF::WorkMode eWorkMode );

    /*************************************************
    功能： 设置前置增益
    入参：eFordGain -- 前置增益
    *************************************************************/
    void setFordwardGain( UHF::ForwardGain eFordGain );

    /*************************************************
    功能： 设置带宽
    入参：eBandWidth -- 带宽
    *************************************************************/
    void setBandWidth( UHF::BandWidth eBandWidth );

    /*************************************************
    功能： 单次采样
    *************************************************************/
    void singleSample( void );

    /*************************************************
    输入参数：
        stationName：站名
        deviceName：设备名
    输出参数： NULL
    返回值： NULL
    功能：保存数据文件
    *************************************************************/
    QString saveDataToFile();

    /*************************************************
    函数名： loadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 载入数据
    *************************************************************/
    void loadData();

    /*************************************************
    函数名： deleteData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 删除数据
    *************************************************************/
    void deleteData();

private:
    HistogramChart* m_pChart;//图谱
    ConfigInstance* m_pConfig;//配置模块

    Module::SignalState m_eSignalState;// 信号状态

    Module::SampleMode m_eSampleMode;//采样模式
    UHF::ForwardGain m_eForwardGain;//前置增益
    UHF::BandWidth m_eBandWidth;//带宽
    UINT8 m_ucRedAlarm;//红色报警
    UINT8 m_ucYellowAlarm;//黄色报警

    UHF::SingleSampleState m_eSingleSampleState; // 单次采样状态

    UHF::AmplitudeData m_data;//数据
};

#endif // UHFAMPLITUDEVIEW_H
