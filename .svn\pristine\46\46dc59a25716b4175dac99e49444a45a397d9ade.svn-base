#include "connectview.h"
#include <QDebug>
#include <QApplication>
#include <QBoxLayout>
#include <QTimer>
#ifdef Q_OS_LINUX
#include <wifi.h>
#endif
#include "systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.h"
#include "systemsetview/systemsetabout/wifisetting/wifisettingview.h"
#include "mobileaccessservice.h"
#include "titlebar/TitleBar.h"
#include "systemsetview/systemsetabout/common/settingswitch.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "systemsetting/wifiservice.h"



namespace CustomAccessView
{

const int TITLE_HEIGHT = 100;    // 标题栏的高度

typedef enum _Function
{
    WIFI_CONNECT = 0,
    BLUETOOTH_CONNECT,
    FUNCTION_COUNT
}Function;

}

/************************************************
 * 功能     : 构造函数
 * 输入参数 :
 *      pConfig -- 配置
 *      strBackgroundIcon -- 背景图片
 *      parent -- 父窗体
 ************************************************/
ConnectView::ConnectView(QWidget *parent)
    : QWidget(parent)
{
    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();

    // 定义标题栏，并指定高度
    TitleBar* pConnectTitle = new TitleBar(trUtf8("Connect Terminal"), this);
    pConnectTitle->setFixedHeight( CustomAccessView::TITLE_HEIGHT );
    pConnectTitle->setFixedWidth( Window::WIDTH);
    pConnectTitle->setFocusPolicy(Qt::StrongFocus);
    pConnectTitle->setFocus();
    connect(pConnectTitle, SIGNAL(sigClicked()), this, SLOT(close()));

    m_pWifiSwitch = new SettingSwitch(false, trUtf8("Hotspot Connection"), this);
    m_pWifiSwitch->setFixedHeight(120);
    connect(m_pWifiSwitch, SIGNAL(sigSwitchStatusChanged(bool)), this, SLOT(wifiStateChanged(bool)));

    m_pBluetoothSwitch = new SettingSwitch(false, trUtf8("Bluetooth Connection"), this);
    m_pBluetoothSwitch->setFixedHeight(120);
    connect(m_pBluetoothSwitch, SIGNAL(sigSwitchStatusChanged(bool)), this, SLOT(bluetoothStateChanged(bool)));


    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin( 0 );
    pMainLayout->addWidget( pConnectTitle, 0, Qt::AlignTop);
    if(SystemSet::ACCESS_WIFI_MODE == m_eAccessMode)
    {
        pMainLayout->addWidget(m_pWifiSwitch);
    }

    if (SystemSet::ACCESS_BLUETOOTH_MODE == m_eAccessMode)
    {
        pMainLayout->addWidget(m_pBluetoothSwitch);
    }

    pMainLayout->addStretch();
    setLayout(pMainLayout);

    setFixedSize(Window::WIDTH, Window::HEIGHT);
    setWindowFlags( Qt::FramelessWindowHint);
    setAttribute( Qt::WA_DeleteOnClose);

    initDatas();

    bool bWifiOn =  MobileAccessService::instance()->isConnected(TCP_MODE);
    bool bBluetoothOn = MobileAccessService::instance()->isConnected(BlueTooth_MODE);
    m_bWifiResult = false;

    m_pWifiSwitch->setSwitchState(bWifiOn);
    m_pBluetoothSwitch->setSwitchState(bBluetoothOn);

    //added
    connect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), this, SLOT(showBluetoothConnectionResult(bool)));
    connect(MobileAccessService::instance(), SIGNAL(sigWifiStatusChanged(bool)), this, SLOT(showWifiConnectionResult(bool)));

    if(SystemSet::ACCESS_BLUETOOTH_MODE == m_eAccessMode)
    {
        m_pWifiSwitch->hide();
    }
    else if (SystemSet::ACCESS_WIFI_MODE == m_eAccessMode)
    {
        m_pBluetoothSwitch->hide();
    }
    else
    {
        m_pWifiSwitch->hide();
        m_pBluetoothSwitch->hide();
    }

}

/************************************************
 * 功能     : 析构函数
 ************************************************/
ConnectView::~ConnectView()
{
}

void ConnectView::wifiStateChanged(bool bSwitchOn)
{
    if(SystemSet::ACCESS_WIFI_MODE == m_eAccessMode)
    {
        bool bWifiOn =  MobileAccessService::instance()->isConnected(TCP_MODE);
        bool bBluetoothOn = MobileAccessService::instance()->isConnected(BlueTooth_MODE);
        if(bSwitchOn != bWifiOn)
        {
            if(bWifiOn && !bBluetoothOn)//wifi on->off
            {
                if(MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to disconnect?")))
                {
                    MobileAccessService::instance()->disconnectHost(TCP_MODE);
                }
                else
                {
                    m_pWifiSwitch->setSwitchState(true);
                }
            }
            else if(!bWifiOn && bBluetoothOn)//wifi off->on
            {
                MobileAccessService::instance()->disconnectHost(BlueTooth_MODE);
                m_pBluetoothSwitch->setSwitchState(false);
                if(!WifiService::isConnected())
                {
                    WifiSettingView *pView = new WifiSettingView;
                    pView->setFixedSize(Window::WIDTH, Window::HEIGHT);
                    connect(pView, SIGNAL(destroyed(QObject*)), this, SLOT(connectByWifi()));
                    pView->show();
                }
                else
                {
                    connectByWifi();
                }
            }
            else if (!bWifiOn && !bBluetoothOn)//wifi off->on
            {
                if(!WifiService::isConnected())
                {
                    WifiSettingView *pView = new WifiSettingView;
                    pView->setFixedSize(Window::WIDTH, Window::HEIGHT);
                    connect(pView, SIGNAL(destroyed(QObject*)), this, SLOT(connectByWifi()));
                    pView->show();
                }
                else
                {
                    connectByWifi();
                }
            }
            else
            {
                qWarning("ConnectView:: error.");
            }
        }
    }

    return;
}

void ConnectView::bluetoothStateChanged(bool bSwitchOn)
{
    //bool bWifiOn =  MobileAccessService::instance()->isConnected(TCP_MODE);
    bool bBluetoothOn = MobileAccessService::instance()->isConnected(BlueTooth_MODE);
    if(bSwitchOn != bBluetoothOn)
    {
        if(bBluetoothOn)//close bt
        {
            if(MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to disconnect?")))
            {
                MobileAccessService::instance()->disconnectHost(BlueTooth_MODE);
                m_pBluetoothSwitch->setSwitchState(false);
            }
            else
            {
                m_pBluetoothSwitch->setSwitchState(true);
            }
        }
        else //open bt
        {
            if(MsgBox::OK == MsgBox::question("", trUtf8("Confirm to connect bluetooth?")))
            {
                MobileAccessService::instance()->disconnectHost(TCP_MODE);
                if(SystemSet::ACCESS_WIFI_MODE == m_eAccessMode)
                {
                    m_pWifiSwitch->setSwitchState(false);
                }

                //MobileAccessService::instance()->connectHost(BlueTooth_MODE);
                disconnect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), this, SLOT(showBluetoothConnectionResult(bool)));
                m_pBTSettingsView = new BlueToothSettingView(true);
                connect(m_pBTSettingsView, SIGNAL(destroyed(QObject*)), this, SLOT(connectByBluetooth()));

                //test
                disconnect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), m_pBTSettingsView, SLOT(onUpdateBluetoothConnectionResult(bool)));
                connect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), m_pBTSettingsView, SLOT(onUpdateBluetoothConnectionResult(bool)));

                m_pBTSettingsView->setFixedSize(Window::WIDTH, Window::HEIGHT);
                m_pBTSettingsView->show();
            }
            else
            {
                m_pBluetoothSwitch->setSwitchState(false);
            }
        }
    }
    else
    {
        //do nothing
    }

    return;
}

void ConnectView::showWifiConnectionResult(bool bResult)
{
    if(SystemSet::ACCESS_WIFI_MODE == m_eAccessMode)
    {
        QString strInfo = "";
        if(bResult)
        {
            strInfo = QObject::trUtf8("Access app success!");
            m_pWifiSwitch->setSwitchState(true);
            m_bWifiResult = true;
        }
        else
        {
            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            QString strServerIP = pConfig->value(APPConfig::KEY_CUSTOM_ACCESS_SERVER_IP);
            QString strServerPort = pConfig->value(APPConfig::KEY_CUSTOM_ACCESS_SERVER_PORT);
            pConfig->endGroup();

            if (strServerIP.isEmpty() || strServerPort.isEmpty())
            {
                strInfo = QObject::trUtf8("Please set the correct ip and port.");
            }
            else
            {
                strInfo = QObject::trUtf8("Please confirm the server to be started.");
            }

            m_pWifiSwitch->setSwitchState(false);
        }

        if(isActiveWindow())
        {
            MsgBox::information("", strInfo);
        }
    }

    return;
}

void ConnectView::WifiConnectedFailed()
{
    if(!m_bWifiResult)
    {
        showWifiConnectionResult(false);
        //MobileAccessService::instance()->disconnectHost(TCP_MODE);
    }
    return;
}

void ConnectView::showBluetoothConnectionResult(bool bResult)
{
    QString strInfo = "";
    if(bResult)
    {
        strInfo = QObject::trUtf8("Access app success!");
        m_pBluetoothSwitch->setSwitchState(true);
    }
    else
    {
        strInfo = QObject::trUtf8("Access app fail!");
        disconnect(m_pBluetoothSwitch,SIGNAL(sigSwitchStatusChanged(bool)), this, SLOT(bluetoothStateChanged(bool)));
        m_pBluetoothSwitch->setSwitchState(false);
        connect(m_pBluetoothSwitch,SIGNAL(sigSwitchStatusChanged(bool)), this, SLOT(bluetoothStateChanged(bool)));
    }

    if(isActiveWindow())
    {
        MsgBox::information("", strInfo);
    }

    return;
}

void ConnectView::initDatas()
{
    return;
}

void ConnectView::connectByBluetooth()
{
    bool bBluetoothOn = MobileAccessService::instance()->isConnected(BlueTooth_MODE);
    showBluetoothConnectionResult(bBluetoothOn);

    disconnect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), this, SLOT(showBluetoothConnectionResult(bool)));
    connect(MobileAccessService::instance(), SIGNAL(sigBluetoothStatusChanged(bool)), this, SLOT(showBluetoothConnectionResult(bool)));
    return;
}

void ConnectView::connectByWifi()
{
    MobileAccessService::instance()->setConnectIPPort();
    MobileAccessService::instance()->connectHost(TCP_MODE);

    QString strInfo = QObject::trUtf8("Connecting ...");
    MsgBox *pWaitMsgBox = new MsgBox(MsgBox::INFORMATION);
    pWaitMsgBox->setInfo("", strInfo, MsgBox::OK);
    pWaitMsgBox->setWindowModality(Qt::ApplicationModal);
    pWaitMsgBox->show();

    disconnect(MobileAccessService::instance(), SIGNAL(sigWifiStatusChanged(bool)), pWaitMsgBox, SLOT(accept()));
    disconnect(MobileAccessService::instance(), SIGNAL(sigWifiStatusChanged(bool)), this, SLOT(showWifiConnectionResult(bool)));

    connect(MobileAccessService::instance(), SIGNAL(sigWifiStatusChanged(bool)), pWaitMsgBox, SLOT(accept()));
    connect(MobileAccessService::instance(), SIGNAL(sigWifiStatusChanged(bool)), this, SLOT(showWifiConnectionResult(bool)));
    QTimer::singleShot(10000, pWaitMsgBox, SLOT(close()));
    QTimer::singleShot(10000, this, SLOT(WifiConnectedFailed()));

    return;
}

/*************************************************
函数名： keyPressEvent
输入参数:
    event -- 事件
输出参数：NULL
返回值： NULL
功能： 键盘事件
*************************************************************/
void ConnectView::keyPressEvent( QKeyEvent* event )
{
    if((event->key() == Qt::Key_Escape))        // esc关闭
    {
        close();
    }
    else if(event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return)
    {
        bool bOpen = !(MobileAccessService::instance()->isConnected(BlueTooth_MODE));
        m_pBluetoothSwitch->setSwitchState(bOpen, true);
    }
    else
    {
        QWidget::keyPressEvent(event);
    }

    return;
}

