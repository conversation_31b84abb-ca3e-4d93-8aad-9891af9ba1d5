#ifndef APPFONTMANAGER_H
#define APPFONTMANAGER_H

#include <QObject>
#include <QFont>

//#include "../global_def.h"

#define DEFALUT_FONT_SIZE 5

namespace LibLanguageFont
{
    typedef enum _LanuageOpition
    {
        SET_ZH_CN = 0,
        SET_ZH_TW,
        SET_EN,
        SET_LANUAGE_COUNT,
        SET_MIN = SET_ZH_CN,
        SET_MAX = SET_LANUAGE_COUNT - 1,
    }LanuageOpition;

    const char* const FONT_LIB_DEFAULT  = "DejaVu Sans";         //DejaVu Sans  msyh  simhei
    const char* const FONT_LIB_ZHCN     = "simhei";
    const char* const FONT_LIB_ZHTW     = "simhei";
    const char* const FONT_LIB_EN       = "simhei";
}


class AppFontManager
{
private:
    AppFontManager();
    ~AppFontManager();

public:
    //获取字体管理类实例
    static AppFontManager* instance();

    //提交修改，针对app应用的全局设置之后需进行提交修改到app应用全局中
    void commit();

    //获取app应用的全局字体
    QFont getAppCurFont();

    //获取app应用全局字体对应的字体族名
    QString getAppCurFontFamily();

    //根据字体族名设置字体相关属性，但不调用commit情况下，不会生效于app全局中
    void setFontFamily(QString qstrFamily);

    //设置字体大小，但不调用commit情况下，不会生效于app全局中
    void setFontSize(int iSize);

    //设置字体加粗属性，但不调用commit情况下，不会生效于app全局中
    void setFontBold(bool bBold);

    //根据语言属性，设置程序中指定的字体信息，但不调用commit情况下，不会生效于app全局中
    void setFontFamilyWithLanguage(LibLanguageFont::LanuageOpition enLanguage);

    //获取应用当前语言对应的字体库
    QString getAppCurFontLibrary();

    //获取应用当前的语言类型
    LibLanguageFont::LanuageOpition getAppCurLanguage();

private:
    QFont m_qFont;
    LibLanguageFont::LanuageOpition m_enLanguage;
    QString m_qstrFontLib;
};

#endif // APPFONTMANAGER_H
