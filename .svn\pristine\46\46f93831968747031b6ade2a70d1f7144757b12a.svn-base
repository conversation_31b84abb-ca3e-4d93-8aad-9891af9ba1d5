#include "aephasetask.h"
#include "ae/AEPhaseService.h"
#include <QTimerEvent>
#include "config/ConfigManager.h"
#include "ae/AEConfig.h"
#include "ae/aephasedatamap.h"
#include "peripheral/peripheralservice.h"

AEPhaseTask::AEPhaseTask(int iMaxTimeout, QObject *parent) : AbstractSpectrumTask(parent)
{
    m_iMaxTimeout = iMaxTimeout;
    m_pAEService = AEPhaseService::instance();
}

AEPhaseTask::~AEPhaseTask()
{
    m_pAEService->deleteUser( m_userId );
}

void AEPhaseTask::startSample()
{
    //m_pAEService->currentChartData( m_vecAEPhaseValidData );
    if( m_vecAEPhaseValidData.isEmpty() )
    {
        PeripheralService *pPeripherService = PeripheralService::instance();
        pPeripherService->openAEPower();

        //注册用户
        MultiServiceNS::SampleUser userInfo;
        userInfo.eSampleType = MultiServiceNS::SPECTRUM_AE_PHASE;
        m_userId = m_pAEService->addUser( userInfo );
        //设置参数
        setAEPhaseParameters();
        //开始采集
        m_iMaxTimer = startTimer( m_iMaxTimeout  );
        connect( m_pAEService, SIGNAL(sigData(QVector<AE::PhaseData>,MultiServiceNS::USERID)),
                 this, SLOT(onDataRead(QVector<AE::PhaseData>,MultiServiceNS::USERID)) );
        connect( m_pAEService, SIGNAL(sigReadAEPhaseDataFailed(MultiServiceNS::USERID)),
                 this, SLOT(onReadDataFailed(MultiServiceNS::USERID)) );
        m_pAEService->startSample( m_userId );
        //m_pAEService->startExclusiveSample( m_userId );
    }
    else
    {
        //设置参数
        setAEPhaseParameters();
        fulfillTask();
    }
}

void AEPhaseTask::onDataRead( QVector<AE::PhaseData> vecData, MultiServiceNS::USERID ucID )
{
    if( ucID == m_userId )
    {
        //设置数据文件里图谱数据
        int dataSize = vecData.size();
        for(int i = 0; i < dataSize; i ++)
        {
            if(vecData.at(i).fPeakValue > m_iAEPhaseTriggerValue)
            {
                m_vecAEPhaseValidData.append(vecData.at(i));
            }
        }

        //完成采样
        if(m_vecAEPhaseValidData.size() >= 1000)
        {
            fulfillTask();
        }
    }
}

void AEPhaseTask::onReadDataFailed(MultiServiceNS::USERID  ucID)
{
    if( ucID == m_userId )
    {
        fulfillTask();
    }
}

void AEPhaseTask::saveMapData()
{
    //创建图谱保存对象
    AEPhaseDataMap * pMap = new AEPhaseDataMap;

    //设置头部信息
    pMap->setSpectrumProperty(DataFileNS::PROPERTY_TEST);

    //设置ext信息
    fillAEPhaseInfo( pMap );
    //设置数据内容
    fillAEPhaseData( pMap );

    emit sigSampleFinished( pMap );
}

void AEPhaseTask::fulfillTask()
{
    //停止采样
    if( m_iMaxTimer != -1 )
    {
        killTimer( m_iMaxTimer );
        m_iMaxTimer = -1;
    }
    //if( m_vecAEPhaseValidData.isEmpty() )
    {
        m_pAEService->stopSample( m_userId );
        //m_pAEService->stopExclusiveSample( m_userId );
        disconnect( m_pAEService, SIGNAL(sigData(QVector<AE::PhaseData>,MultiServiceNS::USERID)),
                 this, SLOT(onDataRead(QVector<AE::PhaseData>,MultiServiceNS::USERID)) );
        disconnect( m_pAEService, SIGNAL(sigReadAEPhaseDataFailed(MultiServiceNS::USERID)),
                 this, SLOT(onReadDataFailed(MultiServiceNS::USERID)) );
    }

    if( m_vecAEPhaseValidData.size() > 1000 )
    {
        m_vecAEPhaseValidData = m_vecAEPhaseValidData.mid(0,1000);
    }

    saveMapData();
    m_vecAEPhaseValidData.clear();

    PeripheralService *pPeripherService = PeripheralService::instance();
    pPeripherService->closeAEPower();
}

void AEPhaseTask::fillAEPhaseInfo( AEPhaseDataMap * pMap )
{
    pMap->setDataPrimitiveType( DataFileNS::DATA_TYPE_FLOAT );
    pMap->setTestChannelSign( AE::CHANNEL_DEFAULT );

    AEMapNS::AEPhaseMapInfo mapInfo;
    mapInfo.fAmpLowerLimit = 0;
    if( (m_eGain >= 0) && (m_eGain < 3) )
    {
        mapInfo.fAmpUpperLimit = AE::Y_RANGE_VALUES[m_eGain];
    }
    else
    {
        qDebug() << "~~~~~~~~~~~~~~~~~~~~~~ m_eGain : " << m_eGain;
    }
    mapInfo.eTransformerType = AEMapNS::AE_TRANSFORMER_AIR;
    mapInfo.usShutTime = m_usCloseDoorTime;
    mapInfo.eSyncSource = (DataFileNS::SyncSource)m_eSyncSource;
    mapInfo.iDataPointNum = m_vecAEPhaseValidData.size();

    pMap->setInfo( &mapInfo );
}

void AEPhaseTask::fillAEPhaseData(AEPhaseDataMap *pMap )
{
    int iDataPointNum = m_vecAEPhaseValidData.size();
    float *pfData = new float[2*iDataPointNum];

    for( int i = 0; i < iDataPointNum; ++i )
    {
        pfData[2*i] = m_vecAEPhaseValidData[i].fPhaseValue;
        pfData[2*i +1] = m_vecAEPhaseValidData[i].fPeakValue;
    }

    //pData = (void*)pfData;
    pMap->setData(pfData, iDataPointNum);

    delete[] pfData;
}

void AEPhaseTask::timerEvent(QTimerEvent *e)
{
    if( e->timerId() == m_iMaxTimer )
    {
        //完成采样
        fulfillTask();
    }
}

void AEPhaseTask::setAEPhaseParameters()
{
    //读取采样参数
    int iGroup = AE::GROUP_AE_PHASE;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_AE );

    m_eGain = (AE::GainType)pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eSyncSource = (Module::SyncSource)(pConfig->value( AE::KEY_SYNC_SOURCE ).toUInt());
    AE::TriggerValue eTriggerValue = (AE::TriggerValue)pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_iAEPhaseTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,eTriggerValue );
    m_usCloseDoorTime = pConfig->value( AE::KEY_CLOSE_DOOR_TIME, iGroup ).toUInt();
    pConfig->endGroup();

    //设置采样参数
    m_pAEService->transaction();
    m_pAEService->setWorkMode( AE::MODE_PHASE );
    m_pAEService->setGain( m_eGain );
    m_pAEService->setSyncSource( m_eSyncSource );
    m_pAEService->setTriggerValue( eTriggerValue );
    m_pAEService->setCloseDoorTime( AE::MODE_PHASE,m_usCloseDoorTime );
    m_pAEService->setUnit( AE::UNIT_DEFAULT );
    m_pAEService->commit();
}
