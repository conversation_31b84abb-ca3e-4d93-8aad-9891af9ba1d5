/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* AEService.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年12月11日
* 摘要：录音播放服务模块接口定义

* 当前版本：1.0
*/
#ifndef RECORDPLAYSERVICE_H
#define RECORDPLAYSERVICE_H
#include "model/HCService.h"
#include "RecordPlay.h"
#include "module_global.h"
#include "datadefine.h"


class RecordPlayServicePrivate;
class MODULESHARED_EXPORT RecordPlayService : public HCService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static RecordPlayService* instance();

    /*************************************************
    功能： 启动业务
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    bool start(void);

    /*************************************************
    功能： 终止业务
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    bool stop(void);

    /*************************************************
    功能： 设置类型
    输入参数：
            eType -- 类型
    *************************************************/
    void setRecordType(RecordPlay::Type eType, RecordPlay::AEType eAEType);

    /*************************************************
    功能： 启动监听AE声音
    *************************************************/
    bool startListenAE();

    /*************************************************
    功能： 停止侦听AE声音
    *************************************************/
    bool stopListenAE();

    /*************************************************
    功能： 启动录音
    输入参数：
            strRecordFile -- 录音文件名
    返回：
            true -- 成功
            其它 -- 失败
    *************************************************/
    bool startRecord(const QString &strRecordFile);

    /*************************************************
    功能： 暂停录音
    说明：考虑到实际使用过程中，不使用暂停和恢复功能，于是新版底层库不支持暂停和恢复功能，2019.06.03
    *************************************************/
    void pauseRecord();

    /*************************************************
    功能： 恢复录音
    说明：考虑到实际使用过程中，不使用暂停和恢复功能，于是新版底层库不支持暂停和恢复功能，2019.06.03
    *************************************************/
    void resumeRecord();

    /*************************************************
    功能： 停止录音
    *************************************************/
    void stopRecord();


    /************************************************
     * 功能：获取音频文件中总时长，单位秒
     * 输入参数：
     *      qstrFilePath：文件全路径
     * 返回值：
     *      总时长（单位秒）
     * **********************************************/
    int getAudioFileTotalTimeSec(const QString &qstrFilePath);

    /*************************************************
    功能： 启动播放
    输入参数：
            strFile -- 播放的文件名
            iStartTime -- 起始时间
    返回：
            true -- 成功
            其它 -- 失败
    *************************************************/
    bool startPlay(const QString& strFile, qint32 iStartTime);

    /*************************************************
    功能： 暂停播放
    *************************************************/
    void pausePlay();

    /*************************************************
    功能：seek到指定位置播放
    *************************************************/
    void seekPlay(int iTimeSec);

    /*************************************************
    功能： 恢复播放
    *************************************************/
    void resumePlay();

    /*************************************************
    功能： 停止播放
    *************************************************/
    void stopPlay();

    /*************************************************
    功能： 设置音量
    输入参数：
            ucVolume -- 音量
    *************************************************/
    bool setVolume(quint8 ucVolume);

signals:
    /*************************************************
    功能： 播放的结果
    *************************************************/
    void sigPlayResult(int iResult);
    /*************************************************
    功能： 录音的结果
    *************************************************/
    void sigRecordResult(int iResult);
    /*************************************************
    功能： 监听AE结果
    *************************************************/
    void sigListenAEResult(int iResult);

    /*************************************************
    功能： 释放系统音量变化信号
    *************************************************/
    void sigSysVolume(quint8 qui8Volume);

private:
    /****************************
    功能： 构造函数
    *****************************/
    RecordPlayService();

    /****************************
    功能： 析构函数
    *****************************/
    ~RecordPlayService();

    /****************************
    功能： disable 拷贝
    *****************************/
    RecordPlayService(const RecordPlayService& other);

    /****************************
    功能： disable 赋值
    *****************************/
    RecordPlayService & operator = (const RecordPlayService &);

    /************************************
     * 功能：停止底层循环线程
     * *************************************/
    void stopDriverLoop();

private:

    enum State
    {
        IDLE = 0,       //空闲状态
        AE_PLAYING,     //AE播放中
        FILE_PLAYING,   //文件播放中
        MIC_RECORDING,  //mic录音中
    };

    RecordPlayServicePrivate* d;
    friend class RecordPlayServicePrivate;
    State m_eState;
    RecordPlay::Type m_eRecType;
	RecordPlay::AEType m_eAEType;
};

#endif // AEMODULE_H
