#include "tevampplaybackview.h"
#include <QVBoxLayout>
#include "window/Window.h"
#include "tev/tevdefine.h"
#include "datadefine.h"
#include "diagnosismgr/diagnosismanager.h"
#include "log/log.h"

const INT32 TEV_CHART_HEIGHT = 485 + 125;

/*************************************************
函数名： TEVAmpPlaybackView(QObject *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TEVAmpPlaybackView::TEVAmpPlaybackView(QWidget *parent)
    : PlayBackBase(parent)
{
    m_pChart = new HistogramChart(TEV::AMP_MAX_COLUMNS, TEV::CHART_MIN_VALUE, TEV::CHART_MAX_VALUE, TEV_CHART_HEIGHT);
    setCenterWidget(m_pChart);
    m_pChart->setRunningStatus(false);
}

/*************************************************
函数名： playbackFile(const QString &strFileName)
输入参数： strFileName：回放文件名
输出参数： NULL
返回值： NULL
功能： 设置回放文件
*************************************************************/
void TEVAmpPlaybackView::playbackFile(const QString &strFileName)
{
    m_pChart->clear();
    m_pChart->clearDiagRet();
    m_pChart->setRunningStatus(false);

    TEVAmpDataMapSave fDataSave;
    TEVAmpDataInfo stPlayBackDataInfo;
    if(0 == fDataSave.getDataFromFile(strFileName, &stPlayBackDataInfo))
    {
        setStationName(stPlayBackDataInfo.stHeadInfo.strSubstationName);
        setDeviceName(stPlayBackDataInfo.stHeadInfo.strDeviceName);

        INT32 iAmpVal = (INT32)(Module::dealFloatPrecision(stPlayBackDataInfo.fTEVAmp, 0));
        INT32 iMaxAmpVal = (INT32)(Module::dealFloatPrecision(stPlayBackDataInfo.fTEVMax, 0));
        m_pChart->setAlarm((int)(stPlayBackDataInfo.fWarningValue), (int)(stPlayBackDataInfo.fAlarmingValue));
        m_pChart->addSample(iMaxAmpVal);
        m_pChart->addSample(iAmpVal);
        m_pChart->setRunningStatus(false);

        QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(stPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos, DIAG_TEV);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stPlayBackDataInfo.stHeadInfo.ePDDefectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
        stDiagDisplayInfo.qstrPDSignalInfos = stPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos;
        m_pChart->playbackDiagInfo(stDiagDisplayInfo);
    }
    else
    {
        logError(QString("read file (%1) failed.").arg(strFileName).toLatin1().data());
    }

    return;
}
