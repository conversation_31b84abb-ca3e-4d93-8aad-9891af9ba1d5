/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* usbSetting.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月29日
* 摘要：usb设置所在view的定义

* 当前版本：1.0
*/

#ifndef USBSETTING_H
#define USBSETTING_H

#include <QFrame>
#include <QLabel>
#include <QDir>
#include <QLinearGradient>
#include "titlebar/TitleBar.h"
#include "datadefine.h"

typedef enum _FormatResult{
    FORMAT_RESULT_SUCCESS = 0,
    FORMAT_RESULT_FAIL = 1,
    FORMAT_RESULT_ALREADY_FORMAT = 2
}FormatResult;

class usbSetting : public QFrame
{
    Q_OBJECT
public:
    /*************************************************
    函数名： usbSetting(QWidget *parent)
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit usbSetting(QWidget *parent = 0);

protected:
    /************************************************
     * 函数名   : eventFilter
     * 输入参数 : pObj: 面板中的控件;pEvent: 事件
     * 输出参数 : NULL
     * 返回值   : 事件处理结果
     * 功能     : 事件过滤器,处理鼠标点击事件及物理键盘按钮事件
     ************************************************/
    bool eventFilter(QObject *pObj,QEvent *pEvent);

    /*************************************************
    函数名： keyPressEvent
    输入参数:
        event -- 事件
    输出参数：NULL
    返回值： NULL
    功能： 键盘事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

private:
    /*************************************************
    函数名： createMainView(QWidget *parent)
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 创建整体布局
    *************************************************************/
    void createMainView( void );

    /*************************************************
    函数名： refreshCapacity(void)
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 格式化后更新容量显示
    *************************************************************/
    void refreshCapacity( void );

    /*************************************************
    函数名： messageBoxAction( void )
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 格式化数据后消息提示框的处理逻辑
    *************************************************************/
    void messageBoxAction( void );

    /*************************************************
    函数名： recursivlyRemoveDir
    输入参数:
        strPath -- 要删除的目录
        bIsDelSelf -- 是否删除自身
    输出参数：NULL
    返回值： NULL
    功能： 递归方式删除所有文件和目录
    *************************************************************/
    FormatResult recursivlyRemoveDir(QString strPath, bool bIsDelSelf);

    /*************************************************
    功能： 清除设备内部的日志文件，防止日志文件过多，设备启动失败的问题
    *************************************************************/
    void clearLogFiles();

private:
    TitleBar    *m_pTitleBar;           // 标题栏
    QLabel  *m_pDeleteLabel;            // 格式化存储器标签

    QLabel  *m_pAllCapacityLabel;       // 共有容量标签
    QLabel  *m_pAllCapacityTitle;       // 共有容量标题

    QLabel  *m_pUsedCapacityLabel;      // 已用容量标签
    QLabel  *m_pUsedCapacityTitle;      // 已用容量标题

    QLabel  *m_pAvailableCapacityLabel; // 可用容量标签
    QLabel  *m_pAvailableCapacityTitle; // 可用容量标题

};

#endif // USBSETTING_H
