#include "bluetoothappcomm.h"
#include "../module/comm/bluetooth/bluetoothclient.h"
#include "../module/systemsetting/systemsetservice.h"
namespace BLUETOOTH_APPCOMM_SETTING
{
    const quint8 UUID_OPTION = 0;
}
BluetoothAppComm::BluetoothAppComm(QObject *parent) : ISampleAppComm(parent)
{
    initProtocol();

    m_pBluetoothClient = dynamic_cast<BluetoothClient*>( m_pClient );
    if( NULL != m_pBluetoothClient )
    {
        m_bConnected = SystemSetService::instance()->bluetoothSetService()->isBluetoothConnected();
        //挂接BlueTooth状态信号
        connect(m_pBluetoothClient, SIGNAL(sigConnectState(bool)),
                this, SLOT(onLinkStateChanged(bool)));
    }
}

BluetoothAppComm::~BluetoothAppComm()
{
    if( NULL != m_pBluetoothClient )
    {
        delete m_pBluetoothClient;
        m_pBluetoothClient = NULL;
    }
}

/****************************
功能： 设置通讯参数
入参： vParams -- 通信端口的参数集合
出参： NULL
返回值：void
*****************************/
void BluetoothAppComm::setCommParam( const QVector<QString> & vParams )
{
    m_pBluetoothClient->setUUID(vParams.at(BLUETOOTH_APPCOMM_SETTING::UUID_OPTION));
}

/****************************
功能： 创建底层通信组件
入参： NULL
出参： NULL
返回值：底层组件指针
*****************************/
AbstractComm *BluetoothAppComm::createComm()
{
    return SystemSetService::instance()->bluetoothSetService()->getBluetoothClient();
}

/*************************************************
函数名： onLinkStateChanged
输入参数： bState 连接状态
输出参数： NULL
返回值： NULL
功能： bluetooth连接状态改变的处理槽函数
*************************************************************/
void BluetoothAppComm::onLinkStateChanged( bool bState )
{
    if(bState)
    {
        m_bConnected = true;
    }
    else
    {
        m_bConnected = false;
    }
    emit sigConnectionStatusChanged(bState);
}
