#ifndef GLOBAL_LOG_H
#define GLOBAL_LOG_H

#include <QDateTime>
#include <QDebug>

#define CURRENT_TIME (QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz").toLatin1().data())
#define APP_LOG_DEBUG
#define APP_LOG_WARNING
#define APP_LOG_ERROR

#ifdef APP_LOG_DEBUG
#define log_debug(fmt, arg...) \
{\
    qDebug("DEBUG %s, %s, %s, line: %d, " fmt, \
    CURRENT_TIME, __FILE__, __FUNCTION__, __LINE__, ##arg);\
    }
#else
#define log_debug(fmt, arg...)
#endif

#ifdef APP_LOG_WARNING
#define log_warning(fmt, arg...) \
{\
    qWarning("WARN %s, %s, %s, line: %d, " fmt, \
    CURRENT_TIME, __FILE__, __FUNCTION__, __LINE__, ##arg);\
    }
#else
#define log_warning(fmt, arg...)
#endif

#ifdef APP_LOG_ERROR
#define log_error(fmt, arg...) \
{\
    qCritical("ERROR %s, %s, %s, line: %d, " fmt, \
    CURRENT_TIME, __FILE__, __FUNCTION__, __LINE__, ##arg);\
    }
#else
#define log_error(fmt, arg...)
#endif


#endif // GLOBAL_LOG_H

