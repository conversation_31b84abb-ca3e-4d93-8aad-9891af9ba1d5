/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* TitleBar.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月4日
* 摘要：功能视图的标题栏定义

* 当前版本：1.0
*/

#ifndef TITLEBAR_H
#define TITLEBAR_H

#include <QDebug>
#include <QFrame>
#include <QLabel>
#include <QMouseEvent>
#include "Widget.h"
#include "statusbar/StatusBar.h"
struct TitleBarPrivate;
class WIDGET_EXPORT TitleBar : public QFrame
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        title -- 标题
        parent -- 父窗体
    *****************************/
    explicit TitleBar( const QString& title, QWidget* parent = 0 );

    /****************************
    功能： 析构函数
    *****************************/
    ~TitleBar( );

    /****************************
    功能： 设置标题
    输入参数:
        title -- 标题
    *****************************/
    void setTitle( const QString& title );

    /****************************
    功能： 获取标题
    返回值： 标题
    *****************************/
    QString title();

signals:
    /****************************
    功能： 信号，被点击
    *****************************/
    void sigClicked();
protected:
    /****************************
    功能： 重载鼠标点击事件
    输入参数：
            event -- 事件
    *****************************/
    void mousePressEvent(QMouseEvent *event);

    /****************************
    功能： 缩放事件
    输入参数：
            event -- 事件
    *****************************/
    void resizeEvent(QResizeEvent *event);
private:
    /************************************************
     * 功能     : 从配置表生成配置列项
     * 输入参数 :
     *      pItems -- 配置表
     *      iCount -- 配置个数
     * 返回值   :
     *      配置列项
     ************************************************/
    QList<StatusBar::StatusItem> getItemsFromConfig( const StatusBar::StatusItem* pItems, int iCount );

private:
    struct TitleBarPrivate* d;
};

#endif // TITLEBAR_H

