# 项目上下文信息

- PhaseChart项目分析：基于Qt的周期图谱显示组件，支持PRPS/PRPD图谱，包含数据模型(PhaseDataModel)、视图(PhaseChartView)、私有绘制模块等。用户要求严格按源码分析，不生成文档/测试/编译。
- PhaseChart深度架构分析已完成，识别出数据流转效率、绘制引擎、颜色映射、内存管理等关键性能瓶颈。用户需要具体优化实现方案、其他模块问题分析、性能测试方案和重构计划。
- Z200\view\currentdetection目录包含19个源代码文件，实现了电流检测模块的完整功能：
1. 定义文件：currentdetectionviewdefine.h - 包含所有常量、枚举、文本定义
2. 基础类：currentdetectionviewbase.h/cpp - 电流检测视图基类
3. 仪表盘：ammeterwidget.h/cpp - 圆弧仪表盘控件，支持A/mA单位转换，1-3位小数精度
4. 图表组件：currentdetectionchart.h/cpp - 标准电流检测图表
5. 北京版图表：bjcurrentdetectionchart.h/cpp - 北京定制版图表，支持多相电流显示
6. 主视图：currentdetectionview.h/cpp - 标准电流检测视图
7. 北京版视图：bjcurrentdetectionview.h/cpp - 北京定制版视图
8. 回放视图：currentdetectionplaybackview.h/cpp 和 bjcurrentdetectionplaybackview.h/cpp
9. 数值设置：valuesettingdialog.h/cpp - 报警值设置对话框，支持步长调整
- 用户反馈calculateMaxAmplitudeFromPRPD函数问题：实际PRPS值为954mV时，期望算出800.9mV，但实际算出1000.9mV。这表明函数的幅值分度计算逻辑存在精度或映射问题。
- 用户补充信息：如果不加1(即使用i而不是i+1)，实际值645mV时算出的PRPD为400mV。这说明当前的(i+1)公式虽然有偏差，但去掉+1会导致更大的偏差。问题可能在于量化分度的边界定义或映射算法本身。
- 用户提供具体参数：上限10000mV，下限1mV，幅值区间50，相位数60。这些参数用于分析calculateMaxAmplitudeFromPRPD函数的计算逻辑和精度问题。
- 用户澄清期望结果：当i不加1时，645mV期望算出600.94mV，但实际算出400.96mV。这说明存在分度索引偏移问题，实际分度比期望分度少了1。
- 用户提供实际PRPD数据和调试输出：PRPD数据显示分度0和分度3有值33，其他分度为0。maxAmplitude计算结果为600.94mV，dynamicUpperBound为6050mV，最终输出900.191mV。这证实了函数按预期工作，找到了分度3的数据并正确计算出600.94mV。
- 用户询问getMaxAmplitudeValue函数如何修改。从调试输出看，maxAmplitude为600.94mV，但经过getMaxAmplitudeValue处理后dynamicUpperBound变成6050mV，最终输出900.191mV。用户可能希望保持原始的maxAmplitude值而不进行动态量程扩展。
- 用户查看calculatePRPDRangeFromPRPSMax函数并询问是否有四舍五入的可能。该函数模拟PRPS到PRPD的精度损失过程，其中amplitudeSection计算使用了static_cast<int>截断，可能需要考虑四舍五入处理。
- 用户选中了calculatePRPDRangeFromPRPSMax函数代码，该函数模拟PRPS到PRPD的精度损失过程。关键问题在于amplitudeSection计算使用static_cast<int>截断而非四舍五入，这可能导致分度计算不准确，影响最终的动态量程计算结果。
- phasechart\src目录包含35个源代码文件，实现了完整的周期图谱(PRPS/PRPD)显示库：
1. 核心定义：phasedef.h - 全局声明、枚举、类型定义
2. 数据模型：phasedata.h/cpp, phasedatamodel.h/cpp - 周期数据存储和管理
3. 视图系统：phasechartview.h/cpp - 主视图类，支持PRPS/PRPD图谱显示
4. 布局管理：phasechartlayout.h/cpp - 图谱布局和坐标系管理
5. 辅助工具：phasevaluehelper.h/cpp - 数据格式化(电压/dBm/dB)
6. 模型驱动：phasemodeldriver.h/cpp - 定时器驱动的数据推进
7. PRPD矩阵：prpdpointmatrix.h/cpp - 点阵累积和重复率计算
8. private目录：核心实现类包括图表基类、PRPS/PRPD具体实现、绘制引擎、坐标映射、颜色映射等
- Z200\view\PDAUi目录文件上传功能详细分析：
1. 核心上传功能在pdataskview.cpp中实现，包含uploadTask()和upLoadTask()两个主要函数
2. 支持两种通信模式：蓝牙模式(handleBluetoothUpload)和非蓝牙模式(handleNonBluetoothUpload)
3. 文件上传流程：选择任务文件 → 保存当前任务 → 检查登录状态 → 调用PDAService::uploadTasks() → 显示进度对话框
4. 底层实现通过CloudService::uploadTask()进行HTTP上传，支持分块上传和进度回调
5. 数据保存功能在pdacurrenttestdataview.cpp中的saveData()函数，保存电流检测数据到文件
6. 进度显示通过PDAProgressDialog实现，支持取消操作和错误处理
7. 支持RFID模式的文件上传(pdataskrfidview.cpp)和普通模式的文件上传
- Z200\module\webserver目录详细分析：
1. 核心架构：基于Qt HTTP框架的Web服务器模块，采用MVC模式
2. 主要组件：AppWebServer(主服务类)、WebServerBase(基类)、RequestMapper(请求分发器)
3. 控制器模块：16个专用控制器处理不同业务(登录、上传、下载、任务管理、设备信息查询等)
4. 配置系统：使用INI配置文件(webapp1.ini/webapp2.ini)，支持多路径配置文件搜索
5. 协议定义：AppServerDefine.h定义完整的API协议字段和常量
6. 工具类：AppServerUtils提供文件操作、MD5校验、时间戳验证等通用功能
7. 支持功能：文件分块上传下载、任务管理、设备信息查询、网络测试、时间校准等
