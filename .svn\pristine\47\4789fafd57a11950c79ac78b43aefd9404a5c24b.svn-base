/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* measuresettingview.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2020年9月24日
* 摘要：测量参数设置界面

* 当前版本：1.0
*/

#ifndef MEASURESETTINGVIEW_H
#define MEASURESETTINGVIEW_H

#include "systemsetview/systemsetwidget/systemsetview.h"

class MeasureSettingView : public SystemSetView
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
        parent -- 父窗体
    *****************************/
    explicit MeasureSettingView(QWidget *parent = 0);

    ~MeasureSettingView();

protected:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void buttonValueChanged(int id, int iValue);

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void commandButtonPressed(int id);

private:
    /*************************************************
    功能： 设置按钮的显示值
    *************************************************************/
    void setButtonBarDatas(void);

    /*****************************************
     * 功能：初始化按钮栏信息
     * **********************************************/
    void initBtnBarInfo();

private:
    ButtonInfo::Info *m_pBtnInfo;
    quint8 m_qui8BtnCnt;
    bool m_bPrpsBtn;
};

#endif // MEASURESETTINGVIEW_H
