/*
 * Copyright (c) 2019.6，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentdetectionviewdefine.h
 *
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/06/27
 * 摘要：电流检测模块宏定义、常量、结构、枚举等
 * 当前版本：1.0
 */

#pragma once

#include <QtGlobal>

#define CURRENT_DETECTION_VIEW_TRANSLATE( str ) qApp->translate("CurrentDetectionView", (str))

namespace CurrentDetection
{

const char* const CONTEXT = "CurrentDetectionView";  //translate上下文
const char* const TEXT_CURRENT_DETECTION_TITLE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Current Detection");
const char* const TEXT_SAMPLE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Sample");
const char* const TEXT_GROUNDING_ALARM = QT_TRANSLATE_NOOP("CurrentDetectionView", "Grounding Alarm");
const char* const TEXT_PHASE_SELECTION = QT_TRANSLATE_NOOP("CurrentDetectionView", "Phase Selection");
const char* const TEXT_COMPREHENSIVE_ANALYSIS = QT_TRANSLATE_NOOP("CurrentDetectionView", "Comprehensive Analysis");
const char* const TEXT_LOAD_ALARM = QT_TRANSLATE_NOOP("CurrentDetectionView", "Load Alarm");
const char* const TEXT_DELETE_DATA = QT_TRANSLATE_NOOP("CurrentDetectionView", "Delete Data");
const char* const TEXT_SAVE_DATA = QT_TRANSLATE_NOOP("CurrentDetectionView", "Save");
const char* const TEXT_MORE_CONFIG = QT_TRANSLATE_NOOP("CurrentDetectionView", "More...");
const char* const TEXT_LOAD_DATA = QT_TRANSLATE_NOOP("CurrentDetectionView", "Load Data");
const char* const TEXT_START = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Start");
const char* const TEXT_STOP = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Stop");
const char* const TEXT_PHASE_A = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Phase A");
const char* const TEXT_PHASE_B = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Phase B");
const char* const TEXT_PHASE_C = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Phase C");
const char* const TEXT_PHASE_N = QT_TRANSLATE_NOOP_UTF8("CurrentDetectionView", "Phase N");
const char* const TEXT_MODE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Mode");
const char* const TEXT_SINGLE_SAMPLE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Single Sample");
const char* const TEXT_SINGLE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Single");
const char* const TEXT_CONTINIOUS = QT_TRANSLATE_NOOP("CurrentDetectionView", "Continuous");
const char* const TEXT_RFID_SAVE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Save RFID");
const char* const TEXT_RANGE_GEAR = QT_TRANSLATE_NOOP("CurrentDetectionView", "Range Gear");
const char* const TEXT_CABLE_GROUNDING = QT_TRANSLATE_NOOP("CurrentDetectionView", "Cable Grounding");
const char* const TEXT_LOAD_CURRENT = QT_TRANSLATE_NOOP("CurrentDetectionView", "Load Current");
const char* const TEXT_CORE_GROUNDING = QT_TRANSLATE_NOOP("CurrentDetectionView", "Core Grounding");

//电流相别
const char* const TEXT_PHASE_SELECTION_OPTIONS[] =
{
    TEXT_PHASE_A, TEXT_PHASE_B, TEXT_PHASE_C, TEXT_PHASE_N
};

// 采集模式
const char* const TEXT_MODE_OPTIONS[] =
{
    TEXT_SINGLE,
    TEXT_CONTINIOUS,
};

// 量程档位
const char* const TEXT_RANGE_GEAR_OPTIONS[] =
{
    "10A",
    "500A",
    "5000A",
};

}
