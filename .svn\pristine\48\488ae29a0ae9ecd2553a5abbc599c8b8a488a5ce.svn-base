/*****< btpmdbgz.h >***********************************************************/
/*      Copyright 2010 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  BTPMDBGZ - Debug Zone definitions for the Stonestreet One Bluetooth       */
/*             Protocol Stack Platform Manager.                               */
/*                                                                            */
/*  Author:  <PERSON>                                                      */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   05/25/10  D. Lange       Initial creation.                               */
/******************************************************************************/
#ifndef __BTPMDBGZH__
#define __BTPMDBGZH__

   /* Implementation specific Debug Zones.                              */
   /* * NOTE * These cannot clash (or should not overwrite) the         */
   /*          constants that are used by the platform manager debug.   */
   /* * NOTE * This is the starting location for Page 0.                */
#define BTPM_DEBUG_ZONE_                        0x00100000

   /* This is the starting Zone for Zones that are not present in Page  */
   /* zero.                                                             */
#define BTPM_DEBUG_ZONE_NON_PAGE_0_             0x00000100

#endif
