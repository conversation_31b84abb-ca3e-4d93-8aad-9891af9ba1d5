#include "pdataskview.h"
#include <QtConcurrentRun>
#include "PDAViewConfig.h"
#include "window/Window.h"
#include "pda/pdaservice.h"
#include "pda/pdatask.h"
#include "PDAUi/PDAUiView/pdadownloadtaskview.h"
#include "pdapatroltypeview.h"
#include "messageBox/waitmsgbox.h"
#include "pdaloginview.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "PDAUi/PDAUiBean/pdaprogressdialog.h"
#include "PDAUi/PDAUiBean/environmentsettingdialog.h"
#include "controlButton/PopupButton.h"
#include "log/log.h"
#include "global_log.h"
#include "loadingView/loadingdialog.h"
#include "systemsetting/systemsetservice.h"



#define SINGLE_TASK             1
#define DIAG_SHOW_TIME_DELAY    500
#define MINTASK_CNT             8
#define ENABLE_CLICK_MAX_TIME   2000
#define ENABLE_CLICK_MIN_TIME   1000

typedef enum _PDATaskButton
{
    BUTTON_OPEN = 0,        //打开
    BUTTON_DOWNLOAD,        //下载
    BUTTON_UPLOAD,          //上传
    BUTTON_SIFT,            //筛选
    BUTTON_PATROL_MODE,     //巡检模式
    BUTTON_DELETE,          //删除
    BUTTON_ALL_SELECT,      //全选
    BUTTON_SELECT_MODE,     // 选择模式
}PDATaskButton;

//模式
const ButtonInfo::RadioValueConfig s_ModeCfg =
{
    PDAView::TEXT_MODE_OPTIONS, sizeof(PDAView::TEXT_MODE_OPTIONS) / sizeof(char*)
};

//设置巡检跳转模式
const ButtonInfo::RadioValueConfig g_PatrolSwitchModeCfg =
{
    PDAView::g_PatrolSwitchModeSetting, sizeof(PDAView::g_PatrolSwitchModeSetting) / sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_PDAButtonInfo[] =
{
    { BUTTON_OPEN, { ButtonInfo::COMMAND, PDAView::TEXT_OPEN, NULL, "", NULL } },//打开
    { BUTTON_DOWNLOAD, { ButtonInfo::COMMAND, PDAView::TEXT_DOWNLOAD, NULL, "", NULL } },//下载
    { BUTTON_UPLOAD, { ButtonInfo::COMMAND, PDAView::TEXT_UPLOAD, NULL, "", NULL } },//上传
    { BUTTON_SIFT, { ButtonInfo::COMMAND, PDAView::TEXT_SIFT, NULL, "", NULL } },//筛选
    { BUTTON_PATROL_MODE, { ButtonInfo::RADIO, PDAView::TEXT_PATROL_MODE, NULL, "", &g_PatrolSwitchModeCfg } },//巡检模式
    { BUTTON_ALL_SELECT, { ButtonInfo::COMMAND, PDAView::TEXT_ALL_SELECT, NULL, "", NULL } },//全选
    { BUTTON_DELETE, { ButtonInfo::COMMAND, PDAView::TEXT_DELETE, NULL, "", NULL } },//删除
    //{ BUTTON_SELECT_MODE, { ButtonInfo::RADIO, PDAView::TEXT_MODE, NULL, "", &s_ModeCfg } },//按键模式
};

void loadTask(int iIdx)
{
    PDAService::instance()->loadTask(iIdx);
    return;
}

void saveTask()
{
    PDAService::instance()->finishTestingTask();
    return;
}

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
PDATaskView::PDATaskView(QWidget *parent)
    : PDAListView(QObject::trUtf8("Task List"), parent)
    , m_pShiftTaskView(NULL)
    , m_pTaskReadRunnable(NULL)
    , m_pTaskSaveRunnable(NULL)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    PDAService::instance()->setIsLogined(false);
    m_qstrTipsInfo = "";
    m_bClickEnable = false;

    m_ePatrolSwitchMode = SystemSetService::instance()->getPatrolSwitchMode();

    m_pChart->setSelectionMode(PDAListChart::EXTENDED_SELECT);

    //创建按钮栏
    ListPushButtonBar* pButtonBar = createButtonBar(PDAView::CONTEXT, s_PDAButtonInfo, sizeof(s_PDAButtonInfo) / sizeof(ButtonInfo::Info));
    PopupButton* pPatrolModeBtn = static_cast<PopupButton *>(pButtonBar->button(BUTTON_PATROL_MODE));
    if(pPatrolModeBtn)
    {
        pPatrolModeBtn->setPopupMode(PopupWidget::SWITCH_MODE);
        pPatrolModeBtn->setValue(static_cast<int>(m_ePatrolSwitchMode));
    }

    PDAService::instance()->readCloudInfo();            //读取cms网络信息

    // 绑定任务信息发生改变的信号
    qRegisterMetaType<errorProcess::ReplyCode>("errorProcess::ReplyCode");
    qRegisterMetaType<QVector<TaskInfo> >("QVector<TaskInfo>");

    connect(PDAService::instance(), SIGNAL(sigTaskChanged()), this, SLOT(onTaskInfoChanged()));
    connect(PDAService::instance(), SIGNAL(sigCurTaskFinished(QString, QString, int, int)),
            this, SLOT(onCurTaskFinished(QString, QString, int, int)));
    connect(PDAService::instance(), SIGNAL(sigReadTaskInfosFinished(QVector<TaskInfo>)),
            this, SLOT(onShowTaskInfos(QVector<TaskInfo>)));
    connect(PDAService::instance(), SIGNAL(sigSendStorageState()),
            this, SLOT(onShowStorageInfos()));
    initTasksSiftView();
    logInfo("patrol task init ok...");
}

/**************************************
 * 功能：处理显示事件
 * 输入参数：
 *      pEvent：显示事件
 * ************************************/
void PDATaskView::showEvent(QShowEvent *pEvent)
{
    Q_UNUSED(pEvent);
    PDAService::instance()->setOpenState(true);
    initTaskInfos();  //初始化任务列表信息
    return;
}

/**************************************
 * 功能：处理关闭事件
 * 输入参数：
 *      pEvent：关闭事件
 * ************************************/
void PDATaskView::closeEvent(QCloseEvent *pEvent)
{
    Q_UNUSED(pEvent);
    PDAService::instance()->setOpenState(false);
    return;
}

/*************************************************
功能： 析构函数（保存当前任务信息->文件）
*************************************************************/
PDATaskView::~PDATaskView()
{
    PDAService::instance()->stopRequest();
    PDAService::instance()->leavePDA();
    QtConcurrent::run(saveTask);
    APP_CHECK_FREE( m_pShiftTaskView );
    //APP_CHECK_FREE(m_pTaskReadRunnable);  //QThreadPool::globalInstance()->start(), 执行之后自动释放空间，这里不需要调用delete释放
    //APP_CHECK_FREE(m_pTaskSaveRunnable);  //QThreadPool::globalInstance()->start(), 执行之后自动释放空间，这里不需要调用delete释放
}

/*************************************************
功能： 槽，响应登录结果，成功则创建下载任务列表界面，获取任务列表
*************************************************************/
void PDATaskView::createDownloadTaskView()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    QString qsUserName = pConfig->value(APPConfig::KEY_USER_NAME);
    pConfig->endGroup();

    QVector<TaskInfo> cloudTasks;
    cloudTasks.clear();

    PDADownloadTaskView *pView = new PDADownloadTaskView( cloudTasks );
    PDAService::instance()->getTasksInfoFromCloud( qsUserName );
    pView->show();

    return;
}

/*************************************************
功能： 删除选中的任务
*************************************************************/
void PDATaskView::deleteSelectedTasks()
{
    QVector<qint32> selectItems = m_pChart->itemsIndexSelected();
    if(!selectItems.isEmpty())
    {
        if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete?")))
        {
            m_pChart->deleteItems(selectItems);

            QVector<UINT32> tasks;
            tasks.clear();

            for(int i = 0, iSize = selectItems.size(); i < iSize; ++i)
            {
                tasks << selectItems.at(i);
            }

            PDAService::instance()->removeTasks(tasks);

            //删除后，默认选中第一个条目
            if(m_pChart->allItems().size() > 0)
            {
                m_pChart->setCurrentItemSelected(0);
            }
        }
    }
    else
    {
        MsgBox::warning("", trUtf8("No task has been chosen."));
    }
    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void PDATaskView::onCommandButtonPressed( int id )
{
    if(!m_bClickEnable)
    {
        logError("unable to click.");
        return;
    }

    PDAService::instance()->stopRequest();

    switch( id )
    {
    case BUTTON_OPEN:
    {
        onOpenClicked();     //打开任务
        break;
    }
    case BUTTON_DELETE:
    {
        deleteSelectedTasks();      //删除任务
        break;
    }
    case BUTTON_ALL_SELECT:
    {
        m_pChart->setAllItemSelected();     //全选
        break;
    }
    case BUTTON_DOWNLOAD:                   // 下载
    {
        PDAService* pService = PDAService::instance();
        if(pService->isLogined())
        {
            createDownloadTaskView();
        }
        else
        {
            PDALoginView* pView = new PDALoginView;
            connect(pView, SIGNAL(sigLogined()), this, SLOT(createDownloadTaskView()));
            pView->show();
        }
        break;
    }
    case BUTTON_UPLOAD:                     // 上传
    {
        if(!m_pChart->itemsIndexSelected().isEmpty())
        {
            PDAService* pService = PDAService::instance();
            if(pService->currentTask() != NULL)         // 上传之前更新下任务信息到文件
            {
                pService->currentTask()->saveTaskFile();
            }

            //根据登陆状态确定是否进行登陆
            if(pService->isLogined())
            {
                upLoadTask();
            }
            else
            {
                PDALoginView* pView = new PDALoginView;
                connect(pView, SIGNAL(sigLogined()), this, SLOT(upLoadTask()));
                pView->show();
            }
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("No task file has been chosen."));
        }
        break;
    }
    case BUTTON_SIFT:
    {
        if(m_pShiftTaskView)
        {
            m_pShiftTaskView->loadDatas(PDAService::instance()->allTaskInfos());
            m_pShiftTaskView->show();
        }
        break;
    }
    default:
    {
        break;
    }
    }

    return;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void PDATaskView::onButtonValueChanged( int id, int iValue )
{
    //避免按钮值变化了，但是实际后台数据没有更新
    /*if(!m_bClickEnable)
    {
        logError("unable to click.");
        return;
    }*/

    PDAService::instance()->stopRequest();
    switch( id )
    {
    case BUTTON_SELECT_MODE:
    {
        m_pChart->setSelectionMode((PDAListChart::SelectionMode)iValue);
        break;
    }
    case BUTTON_PATROL_MODE:
    {
        SystemSetService::instance()->setPatrolSwitchMode(static_cast<SystemSet::PatrolSwitchMode>(iValue));
        break;
    }
    default:
    {
        break;
    }
    }

    return;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PDATaskView::onItemClicked( int id )
{
    if(!m_bClickEnable)
    {
        logError("unable to click.");
        return;
    }

    if(id != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
    {
        openTask(id);
    }
    else
    {
        logWarning("no item select");
    }

    return;
}

/*************************************************
功能： 处理条目被长按后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PDATaskView::onItemLongClicked( int id )
{
    if(!m_bClickEnable)
    {
        logError("unable to click.");
        return;
    }

    if(id != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
    {
        deleteSelectedTasks();
    }
    else
    {
        logWarning("no item select");
    }

    return;
}

/*************************************************
功能： 槽，响应任务信息发生改变的信号
*************************************************************/
void PDATaskView::onTaskInfoChanged( void )
{
    initTaskInfos();
    return;
}

/*************************************************
功能： 槽，响应当前任务测试完毕的信号
*************************************************************/
void PDATaskView::onCurTaskFinished(QString qstrInnerId, QString qstrName, int iTotalCnt, int iTestedCnt)
{
    Q_UNUSED(qstrInnerId);
    if(m_pChart)
    {
        QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
        for(int i = 0, iSize = itemInfos.size(); i < iSize; ++i)
        {
            PDAListChart::ListItemInfo tmpInfo = itemInfos.at(i);
            if(tmpInfo.m_strId == qstrInnerId)
            {
                tmpInfo.m_strItemName = qstrName;
                tmpInfo.m_iTestedCount = iTestedCnt;
                tmpInfo.m_iTotalCount = iTotalCnt;
                m_pChart->setItemInfo(tmpInfo, i);
                break;
            }
        }
    }

    return;
}

/*************************************************
功能：eCode--下载结果
 通知所有需要上传的任务已经上传完毕
*************************************************/
void PDATaskView::onUploadFinished(ReplyCode eCode)
{
    emit sigUploadFinished();
    if( eCode == REPLY_SUCCESS_CODE)
    {
        MsgBox::information("", QObject::trUtf8("Upload success."));
    }
    else
    {
        QString strMsg;
        strMsg = errorProcess::stateMsgByCode(eCode);
        MsgBox::warning("", strMsg);
    }
}

/*************************************************
功能： 初始化任务信息
*************************************************************/
void PDATaskView::initTaskInfos( void )
{
    /*QVector<TaskInfo> taskInfos(PDAService::instance()->taskInfos());
    QList<PDAListChart::ListItemInfo> itemInfos;

    bool bIsTicked = false;

    for(int i = 0, iSize = taskInfos.size(); i < iSize; ++i)
    {
        itemInfos << PDAListChart::ListItemInfo(taskInfos[i].strItemName,
                                                taskInfos[i].uiTestedCount,
                                                taskInfos[i].uiTotalCount,
                                                bIsTicked);
    }

    if(m_pChart != NULL && !(itemInfos.empty()))
    {
        m_pChart->addItems(itemInfos);
        m_pChart->setCurrentItemSelected(0);
    }*/

    m_bInitingdItems = true;
    m_bClickEnable = false;

    QTimer::singleShot(200, this, SLOT(onWaitReadingTaskInfos()));
    PDAService::instance()->readTaskInfos();

    return;
}

/*********************************************
 * 功能：显示任务信息列表
 * **************************************************/
void PDATaskView::onShowTaskInfos(QVector<TaskInfo> qvt4TaskInfos)
{
    qint32 iSelectedIndex = 0;
    if(m_pChart)
    {
        iSelectedIndex = m_pChart->itemIndexSelected();
        m_pChart->deleteAllItem();
        iSelectedIndex = (PDAListChart::ERROR_SELECTED_INDEX == iSelectedIndex) ? 0 : iSelectedIndex;
    }

    QList<PDAListChart::ListItemInfo> itemInfos;
    itemInfos.clear();

    //加锁处理
    for(int i = 0, iSize = qvt4TaskInfos.size(); i < iSize; ++i)
    {
        itemInfos << PDAListChart::ListItemInfo(qvt4TaskInfos[i].strItemName,
                                                qvt4TaskInfos[i].uiTestedCount,
                                                qvt4TaskInfos[i].uiTotalCount,
                                                false,
                                                PDAListItem::LABEL_MODE,
                                                qvt4TaskInfos[i].strInnerId);
    }

    if(m_pChart && !(itemInfos.empty()) && (SystemSetService::instance()->storageOperEnable())) //若模式为移动磁盘模式,清空Items
    {
        m_pChart->addItems(itemInfos);
        m_pChart->setCurrentItemSelected(static_cast<quint32>(iSelectedIndex));
    }

    m_bInitingdItems = false;
    emit sigReadFinished();

    int iTimeDelay = (MINTASK_CNT < itemInfos.size()) ? ENABLE_CLICK_MAX_TIME : ENABLE_CLICK_MIN_TIME;
    QTimer::singleShot(iTimeDelay, this, SLOT(onEnableClickEvent()));

    return;
}

/*************************************************
功能： 打开按钮点击
*************************************************************/
void PDATaskView::onOpenClicked()
{
    int iSelectItems = m_pChart->itemsIndexSelected().size();
    if(iSelectItems == SINGLE_TASK)                        // 是否选中单个任务，仅允许打开单个
    {
        int iSelectedIndex = m_pChart->itemIndexSelected();
        if(iSelectedIndex != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
        {
            openTask(iSelectedIndex);
        }
        else
        {
            logWarning("no item select");
        }
    }
    else if(iSelectItems > SINGLE_TASK)
    {
        MsgBox::warning("", trUtf8("Unable to open multiple task files."));
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No task file has been chosen."));
    }

    return;
}

/*************************************************
功能： 打开任务
*************************************************************/
void PDATaskView::openTask(int iIdx)
{
    WeatherInfo stWeatherInfo;
    PDAService::instance()->getWeatherTaskInfo(iIdx, stWeatherInfo);

    if (!stWeatherInfo.isWeatherChanged)
    {
        // 判断是否输入温湿度信息
        EnvironmentSettingDialog dlg(stWeatherInfo, this);
        if (QDialog::Accepted == dlg.exec())
        {
            WeatherInfo stNewWeatherInfo;
            dlg.getEnvironmentInfo(stNewWeatherInfo);
            PDAService::instance()->saveTaskInfoSummary(iIdx, stNewWeatherInfo);
        }
    }

    //int iTotalCount = PDAService::instance()->getTaskTotalCount(iIdx);
    //log_debug("Current task total test count: %d", iTotalCount);
    //logDebug(QString("Current task total test count: %1").arg(iTotalCount).toLatin1().data());

    PDAPatrolTypeView *pView = new PDAPatrolTypeView;
    connect(pView, SIGNAL(sigPatrolTaskClose()), this, SLOT(onTaskFinished()));
    pView->show();

    m_qstrTipsInfo = QObject::trUtf8("Reading the task ...");
    QTimer::singleShot(DIAG_SHOW_TIME_DELAY, this, SLOT(onShowTaskOperDialog()));

    //m_pTaskReadRunnable = new TaskReadRunnable(iIdx);
    //QThreadPool::globalInstance()->start(m_pTaskReadRunnable);
    QtConcurrent::run(loadTask, iIdx);

    return;
}

/*************************************************
功能： 上传任务(该接口抽象，仅为过于冗长的代码集中在一个函数内有碍观瞻)
*************************************************************/
void PDATaskView::upLoadTask( void )
{
    // 没有选中条目
    if (m_pChart->itemsIndexSelected().isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("No task file has been chosen."));
        return;
    }

    QVector<UINT32> tasks;
    QVector<qint32> selectItems = m_pChart->itemsIndexSelected();
    for(int i = 0, iSize = selectItems.size(); i < iSize; ++i)
    {
        tasks << selectItems.at(i);
    }

    PDAProgressDialog* pDialog = new PDAProgressDialog();
    connect(PDAService::instance(), SIGNAL(sigUploadProgress(int)), pDialog, SLOT(onProgressChanged(int)));
    connect(PDAService::instance(), SIGNAL(sigUploadFinished(errorProcess::ReplyCode)),
            pDialog, SLOT(onUploadFinished(errorProcess::ReplyCode)));

    PDAService::instance()->uploadTasks(tasks);
    PDAProgressDialog::ProgressState eState = (PDAProgressDialog::ProgressState)pDialog->exec();
    if(eState == PDAProgressDialog::PROGRESS_ING)
    {
        PDAService::instance()->stopUploadTasks();
    }
}

/*************************************************
功能：filter--过滤条件
将筛选界面返回的过滤条件传给pdaservice模块
*************************************************/
void PDATaskView::onLocalTaskFiltered(TaskFilter &filter)
{
    //调用pdaservice接口
    PDAService::instance()->setLocalTaskFilter(filter);
    return;
}

/*************************************************
功能：将筛选界面返回的过滤条件清除
*************************************************/
void PDATaskView::onLocalTaskFilterCleared()
{
    //调用pdaservice接口
    PDAService::instance()->cleanLocalTaskFilter();
    return;
}

void PDATaskView::initTasksSiftView()
{
    APP_CHECK_FREE(m_pShiftTaskView);
    m_pShiftTaskView = new PDASiftTaskView(false);
    connect(m_pShiftTaskView, SIGNAL(sigTaskFilterChanged(TaskFilter&)), this, SLOT(onLocalTaskFiltered(TaskFilter&)));
    connect(m_pShiftTaskView, SIGNAL(sigLocalTaskFilterCleared()), this, SLOT(onLocalTaskFilterCleared()));
    return;
}

/*************************************************
功能： 任务打开和关闭时的对话框
输入参数:NULL
*************************************************************/
void PDATaskView::showTaskOperWaitingDialog(QString qstrMsg)
{
    if(PDAService::instance()->isOperingTask())
    {
        LoadingDialog* pLoadingDialog = new LoadingDialog(qstrMsg, this);
        pLoadingDialog->setWindowModality(Qt::ApplicationModal);
        pLoadingDialog->setAttribute(Qt::WA_DeleteOnClose);
        connect(PDAService::instance(), SIGNAL(sigTaskOperFinished()), pLoadingDialog, SLOT(accept()));

        pLoadingDialog->show();
    }

    m_qstrTipsInfo = "";

    return;
}

/*************************************************
功能： 槽，响应巡检任务关闭事件
输入参数：NULL
*************************************************************/
void PDATaskView::onTaskFinished()
{
    //int iIdx = m_pChart->itemIndexSelected();
    //int iTotalCount = PDAService::instance()->getTaskTotalCount(iIdx);
    //log_debug("Current task total test count: %d", iTotalCount);
    //logDebug(QString("Current task total test count: %1").arg(iTotalCount).toLatin1().data());

    m_qstrTipsInfo = QObject::trUtf8("Saving the task ...");
    QTimer::singleShot(DIAG_SHOW_TIME_DELAY, this, SLOT(onShowTaskOperDialog()));

    //m_pTaskSaveRunnable = new TaskSaveRunnable();
    //QThreadPool::globalInstance()->start(m_pTaskSaveRunnable);
    QtConcurrent::run(saveTask);

    return;
}

/*************************************************
功能： 槽，响应巡检任务文件读取
输入参数：NULL
*************************************************************/
void PDATaskView::onReadTask(int iIdx)
{
    if(!(PDAService::instance()->loadTask(iIdx)))
    {
        PDAPatrolTypeView* pView = new PDAPatrolTypeView;
        connect(pView, SIGNAL(sigPatrolTaskClose()), this, SLOT(onTaskFinished()));
        pView->show();
    }
    else
    {
        qWarning("PDATaskView::onReadTask select task is empty");
    }

    return;
}

/*************************************************
功能： 槽，响应巡检任务文件保存
输入参数：NULL
*************************************************************/
void PDATaskView::onSaveTask()
{
    PDAService::instance()->finishTestingTask();
    return;
}

/*************************************************
 * 响应任务处理时显示等待对话框
 * **************************************************/
void PDATaskView::onShowTaskOperDialog()
{
    showTaskOperWaitingDialog(m_qstrTipsInfo);
    return;
}

/********************************************
 * 功能：槽，显示等待读取对话框
 * ******************************************/
void PDATaskView::onWaitReadingTaskInfos()
{
    if(isActiveWindow())
    {
        LoadingDialog* pLoadingDialog = new LoadingDialog(trUtf8("Reading the task list ..."), this);
        pLoadingDialog->setWindowModality(Qt::ApplicationModal);
        pLoadingDialog->setAttribute(Qt::WA_DeleteOnClose);

        connect(this, SIGNAL(sigReadFinished()), pLoadingDialog, SLOT(accept()));

        if(m_bInitingdItems)
        {
            pLoadingDialog->show();
        }
    }
    else
    {
        logWarning("current window is not activing.");
    }

    return;
}

/********************************************
 * 功能：槽，使能可以点击事件
 * ******************************************/
void PDATaskView::onEnableClickEvent()
{
    m_bClickEnable = true;
    return;
}

/*********************************************
 * 功能：给出提示信息
 * **************************************************/
void PDATaskView::onShowStorageInfos()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("The disk is occupied, please disconnect mobilephone or computer!"));
    }
    return;
}

