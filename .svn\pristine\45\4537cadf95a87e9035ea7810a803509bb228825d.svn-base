/*
* Copyright (c) 2018.10，南京华乘电气科技有限公司
* All rights reserved.
*
* httpbeanpmdt.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2018年10月30日
* 摘要：网络通信组件

* 当前版本：1.0
*/

#ifndef HTTPBEANPMDT_H
#define HTTPBEANPMDT_H

#include <QObject>
#include <QHttpMultiPart>
#include <QNetworkAccessManager>
#include <QSslConfiguration>
#include "module_global.h"
#include "cloud/cloudprotocolpmdt.h"

typedef enum _HttpVerifyMode_
{
    HTTP_VERF_NONE = 0,//不认证
    HTTP_VERF_QUERY,//查询认证，单向的，主要是client认证server
    HTTP_VERF_ALL,//双向认证
    HTTP_VERF_AUTO,//自动认证，QueryPeer for server sockets and VerifyPeer for client sockets
}HttpVerifyMode;

typedef struct _NetConfig_
{
    QString qstrUrl;
    QHttpMultiPart* pHttpMtPart;
    QByteArray qbaReqData;
    QByteArray qbaRspData;
    int iTimeOut;
    int iRetCode;
    CommonRawHead stRawHead;

    _NetConfig_()
    {
        qstrUrl = "";
        pHttpMtPart = NULL;
        qbaReqData.clear();
        qbaRspData.clear();
        iTimeOut = 20000;
        iRetCode = 0;
    }
}NetConfig;

typedef struct _RawHeadData_
{
    QByteArray qbaCodeKey;
    QByteArray qbaMsgKey;
    QByteArray qbaResultKey;
    QByteArray qbaCodeData;
    QByteArray qbaMsgData;
    QByteArray qbaResultData;
    QByteArray qbaRawKey;
    QByteArray qbaRawData;

    _RawHeadData_()
    {
        qbaCodeKey.clear();
        qbaMsgKey.clear();
        qbaResultKey.clear();
        qbaCodeData.clear();
        qbaMsgData.clear();
        qbaResultData.clear();
        qbaRawKey.clear();
        qbaRawData.clear();
    }
}RawHeadData;

typedef struct _SslVerifyCfgInfo_
{
    HttpVerifyMode eMode;//认证模式
    QString qstrCaCertFilePath;//CA信任根证书文件路径，pem格式
    QString qstrLocalCertFilePath;//本地证书文件路径，pem格式
    QString qstrPriKeyFilePath;//私钥文件路径，pem格式
    QByteArray qbaPhrasePwd;//握手密码，可为空

    _SslVerifyCfgInfo_()
    {
        eMode = HTTP_VERF_NONE;
        qstrCaCertFilePath = "";
        qstrLocalCertFilePath = "";
        qstrPriKeyFilePath = "";
        qbaPhrasePwd.clear();
    }

}SslVerifyCfgInfo;


class HttpBeanPmdt : public QObject
{
    Q_OBJECT
public:
    explicit HttpBeanPmdt(QObject *parent = 0);
    ~HttpBeanPmdt();

    //停止上传操作
    void stopUpload();

    //停止下载操作
    void stopDownload();

    //获取数据
    bool getData(NetConfig& stNetCfg);

    //权限获取
    bool postGetAuthData(NetConfig& stNetCfg, QString& qstrAuthData);

    //权限认证
    bool postCheckAuthData(NetConfig& stNetCfg, QString& qstrToken);

    //post方式发送数据
    bool postData(NetConfig& stNetCfg);

    //post放式，采用multipart形式发送form-data
    bool postFormData(NetConfig& stNetCfg);

    //上传文件
    bool uploadFile(NetConfig& stNetCfg);

    //下载文件
    bool downloadFile(NetConfig& stNetCfg, RawHeadData& stRawHeadData);

    //停止请求
    void stopRequest();

    /******************************************************
     * 功能：设置ssl认证信息
     * 输入参数：
     *      stSslVerfCfgInfo：ssl认证配置信息
     * *****************************************************/
    void setSslVerifyInfo(const SslVerifyCfgInfo &stSslVerfCfgInfo);

    /******************************************************
     * 功能：非使能ssl认证
     * *****************************************************/
    void disableSslVerify();

signals:

    void sigFileUploadProgress(qint64 i64SentBytes, qint64 i64TotalBytes);

    void sigFileDownloadProgress(qint64 i64RecvBytes, qint64 i64TotalBytes);

    void sigStopUpload();

    void sigStopDownload();

private:
    void addRawHead(QNetworkRequest& stRequest, const NetConfig& stNetCfg);

private:
    bool m_bStop;
    bool m_bHttps;//https形式进行请求
#ifndef QT_NO_OPENSSL
    QSslConfiguration *m_pSslConfig;//ssl认证实例指针
#endif

};

#endif // HTTPBEANPMDT_H
