/*
* Copyright (c) 2017.9，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：RequestMapper.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年9月20日
* 摘要：Web服务的请求分发模块，根据不同的URL方法名调用相应的处理实例
*/
#ifndef REQUESTMAPPER_H
#define REQUESTMAPPER_H

#include <QObject>
#include <QMap>
#include "httpweb/httprequesthandler.h"

using namespace stefanfrings;

class RequestMapper : public HttpRequestHandler
{
    Q_OBJECT
public:
    /************************************************
     * 输入参数 : requestMapFile -- 请求方法名与处理实例映射的配置文件
     *           parent -- 父对象
     * 功能     : 构造函数
     ************************************************/
    explicit RequestMapper(const QString & requestMapFile = "", QObject *parent = 0);

    ~RequestMapper();

    /************************************************
     * 输入参数 : request -- http请求
     *          response -- http响应
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 根据http请求进行http响应处理
     ************************************************/
    virtual void service(HttpRequest& request, HttpResponse& response);

protected:
    /************************************************
     * 输入参数 : request -- http请求
     *          response -- http响应
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 根据http请求方法名进行分发处理
     ************************************************/
    void distributeRequest(HttpRequest& request, HttpResponse& response);

    QMap<QString, HttpRequestHandler *> m_requestHandleMap;     //请求与处理实例的映射表
    HttpRequestHandler * m_pNoFoundRequestHandle;               //未匹配到请求的处理

private:
    /************************************************
     * 输入参数 : requestMapFile -- 请求方法名与处理实例映射的配置文件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 按照配置文件的配置生成请求名与处理实例的映射表
     ************************************************/
    void createRequestHandleMap( const QString & requestMapFile );
};

#endif // REQUESTMAPPER_H
