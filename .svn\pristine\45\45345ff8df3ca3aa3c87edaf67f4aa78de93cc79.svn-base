/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：PrpsFast.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年5月3日
* 摘要： 该文件主要定义接入G100 PRPS采集图谱的基类

* 当前版本：1.0
*/

#ifndef PRPSFAST_H
#define PRPSFAST_H

#include <QWidget>
#include "PrpsBase.h"

class PrpsFast : public PrpsBase
{
    Q_OBJECT
public:
    /****************************
    函数名： PrpsFast(QWidget *parent = 0)
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit PrpsFast(QWidget *parent = 0);

protected:
    /****************************
    函数名： drawCoordinateScale(QWidget *parent = 0)
    输入参数:pointZero：坐标原点;
            pointMax: 纵坐标最大点（幅值最大点）
    输出参数：NULL
    返回值：NULL
    功能： 绘制原点坐标，及纵坐标最大点
    *****************************/
    void drawCoordinateScale(QPainter *painter, const QPoint &pointZero, const QPoint &pointMax);

};

#endif // PRPSFAST_H
