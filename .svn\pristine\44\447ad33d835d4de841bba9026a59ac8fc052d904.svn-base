
#include <QDebug>
#include "SliderPushButton.h"
#include "PushButton.h"

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    iMin:最小值
    iMax:最大值
    iStep:步长
    suffix:量纲
    parent: 父控件指针
*************************************************************/
SliderPushButton::SliderPushButton( const QString& strTitle,
                            int iMin,
                            int iMax,
                            int iStep,
                            const QString& suffix,
                            QWidget *parent )
    :SliderButton( iMin, iMax, iStep, suffix, parent ), m_strSuffix( suffix )
{
    PushButton* pButton = new PushButton( PushButton::TEXT_ONLY );
    pButton->setToolTipEnable(true);
    setButton( pButton );

    PushSliderPopup* pSliderPopup = new PushSliderPopup();
    pSliderPopup->setValues( this->values() );
    pSliderPopup->setSuffix( suffix );

    setPopupWidget( pSliderPopup );

    setTitle( strTitle );
}

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    pDataInfos -- 制定数据
    iCount -- 个数
    suffix:量纲
    parent: 父控件指针
*************************************************************/
SliderPushButton::SliderPushButton( const QString& strTitle,
                            const int *pDataInfos,
                            int iCount,
                            const QString& suffix,
                            QWidget *parent )
    :SliderButton( pDataInfos, iCount, suffix, parent ), m_strSuffix( suffix )
{
    PushButton* pButton = new PushButton( PushButton::TEXT_ONLY );
    pButton->setToolTipEnable(true);
    setButton( pButton );

    PushSliderPopup* pSliderPopup = new PushSliderPopup();
    pSliderPopup->setValues( this->values() );
    pSliderPopup->setSuffix( suffix );

    setPopupWidget( pSliderPopup );

    setTitle( strTitle );
}

/*************************************************
函数名： ~SliderPushButton
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
SliderPushButton::~SliderPushButton()
{
}

/*************************************************
功能： 设置显示格式
输入参数:
    mode -- 显示格式
*************************************************************/
void SliderPushButton::setMode( PushButton::Mode mode )
{
    if( NULL != button() )
    {
        ((PushButton*)button())->setMode( mode );
    }
}

/*************************************************
功能： 设置标题
输入参数:
    strTitle -- 标题
*************************************************************/
void SliderPushButton::setTitle(const QString& strTitle)
{
    SliderButton::setTitle(strTitle);
    if (NULL != button())
    {
        button()->setToolTip(strTitle + ":\n" + content());
    }
}

/*************************************************
功能： 设置数值
输入参数:
    strContent -- 值
*************************************************************/
void SliderPushButton::setContent(const QString& strContent)
{
    SliderButton::setContent(strContent);
    if (NULL != button())
    {
        button()->setToolTip(title() + ":\n" + strContent);
    }
}
