﻿#include "AEAbstractChart.h"
#include <QPainter>
#include "ColorBlt.h"
#include "appfontmanager/appfontmanager.h"

const int LEFT_SIZE = 40;               // 图谱区域距左边线的距离
const int BOTTOM_SIZE = 30;             // 图谱区域距下边线的距离
const int TOP_SIZE = 40;                // 图谱区域距上边线的距离
const int GRADIENT_WIDTH = 20;          // 图谱右侧渐变颜色示例块的宽度
const int COORDINATE_SIZE = 1;          // 图谱区坐标系线宽
const int COORDINATE_LENTH = 6;         // 相位坐标系上小线段的长度
const int TRIGGER_LINE_WIDTH = 3;       // 触发线宽度

const char* const TEXT_PHASE = QT_TRANSLATE_NOOP_UTF8("AEAbstractChart", "Phase[]");               // 相位单位的文本信息
const char* const TEXT_AMPLITUDE = QT_TRANSLATE_NOOP_UTF8("AEAbstractChart", "Amp.[mV]");          // 幅值单位的文本信息
const char* const TEXT_TIME_INTERVAL_MS = QT_TRANSLATE_NOOP_UTF8("AEAbstractChart", "Time Interval[ms]");   // 时间间隔单位的文本信息
const char* const TEXT_TIME_INTERVAL_S = QT_TRANSLATE_NOOP_UTF8("AEAbstractChart", "Time Interval[s]");   // 时间间隔单位的文本信息
const char* const TEXT_TIME_INTERVAL_MIN = QT_TRANSLATE_NOOP_UTF8("AEAbstractChart", "Time Interval[min]");   // 时间间隔单位的文本信息
const int TEXT_SIZE = 18;
const int FONT_SIZE = 20;
const int POINT_SIZE = 2;//数据点半径

#define AE_ABSTRACT_TRANSLATE( str ) qApp->translate( "AEAbstractChart", (str) )
/************************************************
 * 功能     ：构造函数
 * 输入参数  ：eWorkMode -- 工作模式；父对象指针
 ************************************************/
AEAbstractChart::AEAbstractChart(AE::WorkMode eWorkMode, QWidget *parent) :
    QWidget(parent),
    m_eWorkMode(eWorkMode)
{
    if (AE::MODE_PHASE == eWorkMode)
    {
        m_bShowSinusoidal = true;
    }
    else
    {
        m_bShowSinusoidal = false;
    }

    m_uiMaximum = 1;
    m_uiMinimum = 0;
    m_iYMaxValue = 1;
    m_iTrigger = 0;
    m_iOriginX = 0;
    m_iOriginY = 1;
    m_iChartWidth = 0;
    m_iChartHeight = 0;
}

/****************************
输入参数:iYMax:纵坐标最大值，最小值为0
功能： 设置纵坐标最大值
*****************************/
void AEAbstractChart::setYAxisScale( int iYMax )
{
    m_iYMaxValue = iYMax;                     // 存储Y轴最大值
    update();
}

/****************************
输入参数:iMin：X坐标系最小值，iMax：X坐标系最大值
功能： 设置x坐标系范围
*****************************/
void AEAbstractChart::setXAxisScale( int iMin, int iMax )
{
    if( AE::MODE_PHASE == m_eWorkMode )                   // 若工作模式为相位，x轴固定为0~360度
    {
        return;
    }
    else if( iMax <= iMin )
    {
        return;
    }
    m_uiMaximum = iMax;                        // 存储x轴最大值
    m_uiMinimum = iMin;                        // 存储x轴最小值
}

/****************************
输入参数:PhaseFlyData -- 相位/飞行图谱数据
功能： 添加数据
*****************************/
bool AEAbstractChart::addSample(AbstractData data)
{
    bool bSuccess = true;
    if( (m_iChartWidth <= 0) || (m_iChartHeight <= 0) )
    {
        resetParams();
    }

    if(     data.fXscale <= 1.0
            && data.fXscale > NUMBER_ZERO
            && data.fYscale <= 1.0
            && data.fYscale > NUMBER_ZERO )
    {
        float fX = data.fXscale * m_iChartWidth;  // 当添加一组数据时的处理（有相位移动或回放等需求时需使用的接口）
        float fY = data.fYscale  * m_iChartHeight;
        int iIndex = getIndexFromData( fX,fY );
        m_lRawData.append( data );

        addDataToModel( iIndex,
                        fX,
                        fY );  // 添加数据到需刷新的缓存中
        m_lColorIndex.append( iIndex );
        update();                                   // 更新页面
    }
    else
    {
        bSuccess = false;
    }
    return bSuccess;
}

/****************************
输入参数:rawData:保存原始数据的list
功能： 当相位图谱需要移动时或实现回放功能时利用的接口
*****************************/
void AEAbstractChart::addSamples( const QList<AbstractData> &rawData )
{
    m_hCurrentData.clear();
    m_lColorIndex.clear();
    resizeAEVector();                           // 重新定义存放各点颜色信息的容器
    for( int i = 0;i < rawData.size();++i )
    {
        float fX = rawData.at( i ).fXscale * m_iChartWidth;  // 当添加一组数据时的处理（有相位移动或回放等需求时需使用的接口）
        float fY = rawData.at( i ).fYscale * m_iChartHeight;
        if( fX > NUMBER_ZERO && fY > NUMBER_ZERO )
        {
            addDataToModel( getIndexFromData( fX,fY ),
                            fX,
                            fY );  // 添加数据到需刷新的缓存中
        }
    }
    m_lRawData.clear();                                     // 清除原始数据
    m_lRawData = rawData;                                   // 添加新的数据到缓存中
    updateAll();                                            // 更新界面
}

/****************************
输入参数:rawData:保存原始数据的list;
        rawIndex:保存原始数据的出现概率序号(0~255)
功能： 当相位图谱需要移动时或实现回放功能时利用的接口
*****************************/
void AEAbstractChart::addSamples( const QList<AbstractData> &rawData,
                 const QList<int> &colorIndex )
{
    resizeAEVector();
    if( rawData.size() != colorIndex.size() )
    {
        return;
    }
    for( int i = 0;i < rawData.size();++i )
    {
        float fX = rawData.at( i ).fXscale * m_iChartWidth;  // 当添加一组数据时的处理（有相位移动或回放等需求时需使用的接口）
        float fY = rawData.at( i ).fYscale * m_iChartHeight;
        if( fX > NUMBER_ZERO && fY > NUMBER_ZERO )
        {
            addDataToModel( colorIndex.at( i ),
                            fX,
                            fY );  // 添加数据到需刷新的缓存中
        }
    }
    m_lRawData.clear();                                     // 清除原始数据
    m_lRawData = rawData;                                   // 添加新的数据到缓存中
    updateAll();
}

/****************************
返回值：AbstractData 原始数据
功能： 返回存放的原始数据
*****************************/
const QList<AEAbstractChart::AbstractData> &AEAbstractChart::samples( void ) const
{
    return m_lRawData;                              // 返回缓存的原始数据
}

/****************************
返回值：颜色出现的概率集合
功能： 返回存放颜色出现概率的序号集合
*****************************/
const QList<int> &AEAbstractChart::colorIndex( void ) const
{
    return m_lColorIndex;
}

/****************************
输入参数: NULL
功能： 清除显示
*****************************/
void AEAbstractChart::clear( void )
{
    m_lRawData.clear();                             // 清空缓存的原始数据
    m_hCurrentData.clear();
    m_lColorIndex.clear();
    resizeAEVector();                           // 重新定义存放各点颜色信息的容器
    updateAll();                                    // 更新界面
}

/****************************
输入参数:trigger:触发值
功能： 绘制触发线
*****************************/
void AEAbstractChart::setTrigger( int iTrigger )
{
    if( iTrigger <= m_iYMaxValue )                 // 若设置的触发值大于Y轴最大值认为非法操作
    {
        m_iTrigger = iTrigger;                        // 存储触发值，用于绘制界面红色虚线
        update();                                       // 更新界面
    }
}

/****************************
输入参数:fYellow：黄色比例；fRed：红色比例；fBlack：黑色比例
功能： 设置渐变的比例
*****************************/
void AEAbstractChart::setGradient(float fYellow, float fRed, float fBlack )
{
    // 渐变条高度与Y轴等高：从Y轴顶部到Y轴底部
    m_LinearGradient = QLinearGradient( 0, m_iOriginY - m_iChartHeight, 0, m_iOriginY );

    // 经典热力图渐变色谱：蓝->青->绿->黄->橙->红（从低密度到高密度）
    m_LinearGradient.setColorAt( 0.0, Qt::red );                    // 顶部：红色（最高密度）
    m_LinearGradient.setColorAt( 0.2, QColor(255, 165, 0) );        // 橙色（高密度）
    m_LinearGradient.setColorAt( 0.4, Qt::yellow );                 // 黄色（中高密度）
    m_LinearGradient.setColorAt( 0.6, Qt::green );                  // 绿色（中密度）
    m_LinearGradient.setColorAt( 0.8, Qt::cyan );                   // 青色（中低密度）
    m_LinearGradient.setColorAt( 1.0, Qt::blue );                   // 底部：蓝色（最低密度）

    update();
}

/****************************
函数名： paintEvent(QPaintEvent *pEvent);
输入参数:pEvent：绘图事件
输出参数：NULL
返回值：NULL
功能： 重载绘图函数
*****************************/
void AEAbstractChart::paintEvent( QPaintEvent* )
{
    QPainter painter(this);
    drawRawData( &painter );
    drawCoordinate( &painter );

    if (m_bShowSinusoidal)
    {
        drawSinusoidal(&painter);
    }
}

/****************************
函数名： resizeEvent(QResizeEvent *);
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： resize事件处理函数
*****************************/
void AEAbstractChart::resizeEvent( QResizeEvent* )
{
    resetParams();                                  // 重置界面相关参数
}

/****************************
功能： 更新全部数据
*****************************/
void AEAbstractChart::updateAll(void)
{
    m_AEpixmap = QPixmap( width(), height() );  // 刷新整个界面，清空pixmap
    m_AEpixmap.fill( QColor(0, 0, 0, 0) );

    update();
}

/****************************
功能： 用于绘制AE数据
*****************************/
void AEAbstractChart::drawRawData( QPainter* painter )
{
    painter->save();

    QPainter localPainter( &m_AEpixmap );                    // 定义绘图设备为pixmap

    localPainter.setRenderHint(QPainter::Antialiasing,true); // 打开抗锯齿
    localPainter.setBrush( QBrush( m_LinearGradient ) );     // 设置渐变画刷
    localPainter.setPen( Qt::NoPen );                        // 关闭画笔，绘制出的图形没有边框
    // 绘制渐变矩形 - 高度与Y轴等高
    localPainter.drawRect( width() - GRADIENT_WIDTH * 3 / 2,
                      m_iOriginY - m_iChartHeight,
                      GRADIENT_WIDTH,
                      m_iChartHeight ); // 绘制渐变矩形，高度与Y轴完全一致
    localPainter.setBrush( Qt::NoBrush );                    // 关闭画刷

    QHashIterator< int,QList<QPointF> > colorPoint( m_hCurrentData );// 绘制缓存中的数据
    while( colorPoint.hasNext() )
    {
        colorPoint.next();
        localPainter.setBrush( QBrush( getColorFromIndex( colorPoint.key() )));
        localPainter.setPen( Qt::NoPen );
        const QList<QPointF> &pointList = colorPoint.value();
        for(int i = 0; i < pointList.count(); ++i)        // value为point的list
        {
            localPainter.drawEllipse( pointList.at(i),POINT_SIZE,POINT_SIZE );
        }
    }
    localPainter.setRenderHint(QPainter::Antialiasing,false);// 关闭抗锯齿
    m_hCurrentData.clear();                                 // 清除新增添数据的缓存

    painter->drawPixmap( 0,0,m_AEpixmap );               // 绘制pixmap

    painter->restore();
}

/****************************
功能： 用于绘制AE坐标系
*****************************/
void AEAbstractChart::drawCoordinate(QPainter *painter)
{
    painter->save();

    painter->setPen( QPen( Qt::red,TRIGGER_LINE_WIDTH,Qt::DotLine));
    if( 0 != m_iTrigger )
    {
        painter->drawLine( m_iOriginX,
                              m_iOriginY - m_iTrigger * m_iChartHeight / m_iYMaxValue,
                              m_iOriginX + m_iChartWidth,
                              m_iOriginY - m_iTrigger * m_iChartHeight / m_iYMaxValue);
    }                                                                       // 绘制显示触发值所用红色虚线

    // 分别绘制不同类型的线段
    int coordinateTicksCount = 10; // 坐标刻度线数量，根据模式动态计算
    if( AE::MODE_PHASE == m_eWorkMode )
    {
        coordinateTicksCount = 13 + 5; // 相位模式：X轴13条刻度线 + Y轴5条刻度线
    }
    else
    {
        coordinateTicksCount = 13 + 5; // 飞行模式：X轴13条刻度线 + Y轴5条刻度线 {{ AURA-X: Modify - 更新飞行模式刻度数量 }}
    }
    const int mainAxisCount = 2;         // 主坐标轴数量 (X轴和Y轴)
    const int gridLinesCount = (GRID_COUNT_X + 1) + (GRID_COUNT_Y + 1); // 棋盘网格线数量（包括边框）

    for( int i = 0; i < m_lCoordinateLines.size(); ++i )
    {
        if( i < coordinateTicksCount ) // 坐标刻度线
        {
            painter->setPen( QPen( Qt::black, COORDINATE_SIZE, Qt::SolidLine));
        }
        else if( i < coordinateTicksCount + mainAxisCount ) // 主坐标轴
        {
            painter->setPen( QPen( Qt::black, COORDINATE_SIZE + 1, Qt::SolidLine)); // 主轴用粗线
        }
        else // 棋盘网格线
        {
            int gridIndex = i - coordinateTicksCount - mainAxisCount;
            bool isBorderLine = false;

            if( gridIndex < (GRID_COUNT_X + 1) ) // 垂直网格线
            {
                // 第一条和最后一条垂直线是边框
                isBorderLine = (gridIndex == 0 || gridIndex == GRID_COUNT_X);
            }
            else // 水平网格线
            {
                int horizontalIndex = gridIndex - (GRID_COUNT_X + 1);
                // 第一条和最后一条水平线是边框
                isBorderLine = (horizontalIndex == 0 || horizontalIndex == GRID_COUNT_Y);
            }

            if( isBorderLine )
            {
                painter->setPen( QPen( Qt::black, COORDINATE_SIZE + 1, Qt::SolidLine)); // 边框用粗线
            }
            else
            {
                painter->setPen( QPen( Qt::gray, COORDINATE_SIZE, Qt::SolidLine)); // 内部网格用细灰线
            }
        }

        painter->drawLine( m_lCoordinateLines.at( i ) );                 // 绘制线段
    }

    painter->setFont(QFont(AppFontManager::instance()->getAppCurFontFamily(), TEXT_SIZE));
    if( AE::MODE_PHASE == m_eWorkMode )
    {
        painter->drawText( m_iOriginX - TEXT_SIZE / 2, m_iOriginY + BOTTOM_SIZE,
                              QString("0") );
        painter->drawText( m_iOriginX + m_iChartWidth / 6 - TEXT_SIZE + 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("60") );
        painter->drawText( m_iOriginX + m_iChartWidth * 2 / 6 - TEXT_SIZE + 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("120") );
        painter->drawText( m_iOriginX + m_iChartWidth * 3 / 6 - TEXT_SIZE + 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("180") );
        painter->drawText( m_iOriginX + m_iChartWidth * 4 / 6 - TEXT_SIZE + 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("240") );
        painter->drawText( m_iOriginX + m_iChartWidth * 5 / 6 - TEXT_SIZE + 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("300") );
        painter->drawText( m_iOriginX + m_iChartWidth - TEXT_SIZE * 3 / 2,
                              m_iOriginY + BOTTOM_SIZE,
                              QString("360") );
        painter->setFont( QFont( AppFontManager::instance()->getAppCurFontFamily(), FONT_SIZE ) );
        painter->drawText( m_iOriginX + m_iChartWidth / 2 - TEXT_SIZE * 4 / 3,
                              height() - TEXT_SIZE / 4,trUtf8(TEXT_PHASE) );                // 绘制0~360°及单位的文本信息
    }
    else
    {
        int iTimeInterval = 0;
        const char* qsCompany;
        if( m_uiMaximum >= 6e4 )
        {
            iTimeInterval = m_uiMaximum / 6e4;
            qsCompany = TEXT_TIME_INTERVAL_MIN;
        }
        else if( m_uiMaximum >= 1e3 )
        {
            iTimeInterval = m_uiMaximum / 1e3;
            qsCompany = TEXT_TIME_INTERVAL_S;
        }
        else
        {
            iTimeInterval = m_uiMaximum;
            qsCompany = TEXT_TIME_INTERVAL_MS;
        }
        // 绘制X轴的"0"标签
        painter->drawText( m_iOriginX - TEXT_SIZE / 2, m_iOriginY + BOTTOM_SIZE,
                              QString("0") );

        painter->drawText( m_iOriginX + m_iChartWidth / 2 - TEXT_SIZE * 3 - 30,
                              height() - TEXT_SIZE + 12, AE_ABSTRACT_TRANSLATE( qsCompany) );
        painter->setFont( QFont( AppFontManager::instance()->getAppCurFontFamily(), FONT_SIZE ) );
        painter->drawText( m_iOriginX + m_iChartWidth - TEXT_SIZE * 3 / 2,
                              height() - TEXT_SIZE + 12, QString( " %1 " ).arg( iTimeInterval ) );// 绘制时间间隔单位文本
    }
    painter->rotate( -90 );
    painter->drawText( -m_iOriginY + m_iChartHeight / 2 - (TEXT_SIZE * 5 / 2), LEFT_SIZE * 2 / 3, trUtf8(TEXT_AMPLITUDE));
    // 绘制Y轴的"0"标签 - 更靠近Y轴起始点
    painter->drawText( -m_iOriginY , LEFT_SIZE * 2 / 3, QString("0") );
    painter->drawText( -m_iOriginY + m_iChartHeight - TEXT_SIZE,
                          LEFT_SIZE * 2 / 3, QString( "%1" ).arg( m_iYMaxValue ) ); // 绘制图谱左侧幅值单位
    painter->rotate( 90 );

    painter->restore();
}

/****************************
功能： 用于绘制正弦曲线
*****************************/
void AEAbstractChart::drawSinusoidal(QPainter* pPainter)
{
    pPainter->save();

    pPainter->setPen(Qt::blue);
    pPainter->setRenderHint(QPainter::Antialiasing, true);
    pPainter->drawPolyline(m_sinusoidalLine);
    pPainter->setRenderHint(QPainter::Antialiasing, false);

    pPainter->restore();
}

/****************************
功能： 重置界面相关参数
*****************************/
void AEAbstractChart::resetParams( void )
{
    if( AE::MODE_PHASE == m_eWorkMode )
    {
        m_iOriginX = LEFT_SIZE;
        m_iOriginY = height() - BOTTOM_SIZE * 2;
        m_iChartWidth = width() - LEFT_SIZE - GRADIENT_WIDTH * 2;
        m_iChartHeight = height() - TOP_SIZE - BOTTOM_SIZE * 2;
    }
    else
    {
        m_iOriginX = LEFT_SIZE;
        m_iOriginY = height() - BOTTOM_SIZE;
        m_iChartWidth = width() - LEFT_SIZE - GRADIENT_WIDTH * 2;
        m_iChartHeight = height() - TOP_SIZE - BOTTOM_SIZE;        // resize时重置相关界面大小的参数
    }

    if( m_iChartHeight <= 0 || m_iChartWidth <= 0 )
    {
        m_iChartHeight = 1;
        m_iChartWidth = 1;
    }
    setGradient( 0.7, 0.3, 1.0 );                   // 设置右侧图例渐变矩形的颜色分布比例

    makeCoordinateLines();                          // 生成坐标系线段

    // 更新正弦曲线
    const int iPointCount = m_iChartWidth;
    const int iHalfHeight = m_iChartHeight / 2;
    const int iYBase = TOP_SIZE + iHalfHeight + 1;
    m_sinusoidalLine.resize(iPointCount);
    for(int i = 0; i < iPointCount; ++i)
    {
        m_sinusoidalLine[i].setX(m_iOriginX + i);
        m_sinusoidalLine[i].setY(iYBase - iHalfHeight * qSin(2 * M_PI / iPointCount * i));
    }

    m_AEpixmap = QPixmap( width(), height() );
    m_AEpixmap.fill( QColor(0, 0, 0, 0) );          // 预定义一个空的pixmap

    resizeAEVector();                               // 以图谱区域的高度和宽度为依据，定义一个存放颜色信息的容器

    if( !m_lRawData.isEmpty() )
    {
        addSamples( m_lRawData );
    }
}

/****************************
功能： 生成坐标系线段
*****************************/
void AEAbstractChart::makeCoordinateLines( void )
{
    m_lCoordinateLines.clear();

    // 坐标轴刻度线 - 根据工作模式选择不同的刻度间隔
    if( AE::MODE_PHASE == m_eWorkMode )
    {
        // 相位模式：X轴30度一格（12等分），Y轴保持4等分
        for( int i = 0; i <= 12; ++i )
        {
            m_lCoordinateLines << QLine( m_iOriginX + m_iChartWidth * i / 12,
                                         m_iOriginY + COORDINATE_LENTH / 2,
                                         m_iOriginX + m_iChartWidth * i / 12,
                                         m_iOriginY - COORDINATE_LENTH / 2);
        }
        // Y轴刻度线保持原有的4等分
        for( int i = 0; i < 5; ++i )
        {
            m_lCoordinateLines << QLine( m_iOriginX + COORDINATE_LENTH / 2,
                                         m_iOriginY - m_iChartHeight * i / 4,
                                         m_iOriginX - COORDINATE_LENTH / 2,
                                         m_iOriginY - m_iChartHeight * i / 4);
        }
    }
    else
    {
        // 飞行模式：X轴改为12等分，Y轴保持4等分 {{ AURA-X: Modify - 飞行图谱X轴改为12等分 }}
        // X轴刻度线：12等分
        for( int i = 0; i <= 12; ++i )
        {
            m_lCoordinateLines << QLine( m_iOriginX + m_iChartWidth * i / 12,
                                         m_iOriginY + COORDINATE_LENTH / 2,
                                         m_iOriginX + m_iChartWidth * i / 12,
                                         m_iOriginY - COORDINATE_LENTH / 2);
        }
        // Y轴刻度线：保持4等分
        for( int i = 0; i < 5; ++i )
        {
            m_lCoordinateLines << QLine( m_iOriginX + COORDINATE_LENTH / 2,
                                         m_iOriginY - m_iChartHeight * i / 4,
                                         m_iOriginX - COORDINATE_LENTH / 2,
                                         m_iOriginY - m_iChartHeight * i / 4);
        }
    }

    // 主坐标轴（X轴和Y轴）
    m_lCoordinateLines << QLine( m_iOriginX,m_iOriginY,
                                 m_iOriginX,m_iOriginY -m_iChartHeight )
                       << QLine( m_iOriginX,m_iOriginY,
                                 m_iOriginX + m_iChartWidth,m_iOriginY );

    // 棋盘样式网格 - 使用宏定义的正方形网格
    // 绘制垂直网格线（包括边框）
    for( int i = 0; i <= GRID_COUNT_X; ++i )
    {
        int x = m_iOriginX + m_iChartWidth * i / GRID_COUNT_X;
        m_lCoordinateLines << QLine( x, m_iOriginY,
                                     x, m_iOriginY - m_iChartHeight );
    }

    // 绘制水平网格线（包括边框）
    for( int i = 0; i <= GRID_COUNT_Y; ++i )
    {
        int y = m_iOriginY - m_iChartHeight * i / GRID_COUNT_Y;
        m_lCoordinateLines << QLine( m_iOriginX, y,
                                     m_iOriginX + m_iChartWidth, y );
    }
}

/****************************
功能： 定义存放数据颜色索引号的容器
*****************************/
void AEAbstractChart::resizeAEVector( void )
{
    m_vPointsMapVector.clear();
    QVector<int> qVector = QVector<int>( m_iChartHeight,0 );
    m_vPointsMapVector = QVector< QVector<int> >( m_iChartWidth,qVector );  // 定义一个以图谱区域高、宽为依据的容器
}

/****************************
输入参数: usX:X坐标；usY:Y坐标
功能： 根据点的位置得到对应的index
*****************************/
int AEAbstractChart::getIndexFromData( float fX,float fY )
{
    int iX = int( fX + 0.5 );
    int iY = int( fY + 0.5 );
    int iSum = 0;
    if( iX > m_iChartWidth || iY > m_iChartHeight )   // 若该点不在图谱区域范围内返回0
    {
        return 0;
    }
    if( iX == m_iChartWidth )           // 防止下标出界
    {
        iX--;
    }
    if( iY == m_iChartHeight )          // 防止下标出界
    {
        iY--;
    }
    if( iX > 0 )
    {
        iSum += m_vPointsMapVector[iX - 1][iY];          // 若x坐标大于0，则加上该点后面的颜色大小
    }
    if( iX <  m_iChartWidth - 1 )
    {
        iSum += m_vPointsMapVector[iX + 1][iY];          // 若x坐标小于图谱宽度，则加上该点前面的颜色大小
    }
    if( iY > 0)
    {
        iSum += m_vPointsMapVector[iX][iY - 1];          // 若y坐标大于0，则加上该点下面的颜色大小
    }
    if( iY < m_iChartHeight - 1 )
    {
        iSum += m_vPointsMapVector[iX][iY + 1];          // 若y坐标小于高度，则加上该点后面的颜色大小
    }
    int iIndex = m_vPointsMapVector[iX][iY] + iSum / 4 + 4;// 将该点原先的颜色+上下左右颜色的平均值+4的偏移
    if( iIndex > 255)
    {
        iIndex = 255;
    }
    m_vPointsMapVector[iX][iY] = iIndex;                  // 将该点的颜色索引号存入对应的容器中
    //iIndex /= 2;
    return iIndex;
}

/****************************
输入参数: usIndex：对应查表的下标
功能： 根据index查表得到对应的rgb颜色
*****************************/
QColor AEAbstractChart::getColorFromIndex( UINT16 usIndex )
{
    UINT16 usRow = usIndex;
    if( usRow > sizeof( ColorBlt::colorblt ) / sizeof( ColorBlt::colorblt[0] ) - 1) // 因该颜色对照表的颜色只有64个，确保不会超出范围
    {
        usRow = sizeof( ColorBlt::colorblt ) / sizeof( ColorBlt::colorblt[0] ) - 1;
    }
    int r = ColorBlt::colorblt[usRow][0];                // 通过下标将数组中的数分别赋给r，g，b
    int g = ColorBlt::colorblt[usRow][1];
    int b = ColorBlt::colorblt[usRow][2];
    QColor qColor( r,g,b );
    return qColor;
}

int AEAbstractChart::getChartWidth()
{
    return m_iChartWidth;
}

int AEAbstractChart::getChartHeight()
{
    return m_iChartHeight;
}

/****************************
输入参数: iIndex:颜色查表索引号；usWidth：数据点距原点的宽度；usHeight：数据点距原点的高度
功能： 将数据添加到容器中
*****************************/
void AEAbstractChart::addDataToModel( int iIndex,float fWidth,float fHeight )
{
    m_hCurrentData[iIndex] << QPointF( m_iOriginX + fWidth,
                                       m_iOriginY - fHeight ); // 将点存入指定待刷新容器中
}
