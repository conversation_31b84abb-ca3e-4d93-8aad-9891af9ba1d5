#ifndef WIFIINFOSETTINGS_H
#define WIFIINFOSETTINGS_H

#include <QObject>
#include <QSettings>
#include <QList>
#include <QMutex>


typedef struct _WifiSaveInfo_
{
    QString qstrName;
    QString qstrPwd;
	int  iSigStrength;

    _WifiSaveInfo_()
    {
        qstrName = "";
        qstrPwd = "";
		iSigStrength = -100;
    }

    bool operator==(const _WifiSaveInfo_ &stOther)
    {
        return (this->qstrName == stOther.qstrName && this->qstrPwd == stOther.qstrPwd);
    }

}WifiSaveInfo;

class WifiInfoSettings : public QObject
{
    Q_OBJECT
private:
    explicit WifiInfoSettings(QObject *parent = NULL);
    ~WifiInfoSettings();

    void refreshInfos();

public:
    static WifiInfoSettings* instance();

    void saveWifiInfos(const QList<WifiSaveInfo>& qlstSaveWifiInfos);

    QList<WifiSaveInfo> readWifiInfos();

signals:

public slots:

private:
    QSettings* m_pSettings;
    QMutex m_qmt4WifiInfos;
    QList<WifiSaveInfo> m_qlstInfos;        //最多维护最新10条信息
};

#endif // WIFIINFOSETTINGS_H
